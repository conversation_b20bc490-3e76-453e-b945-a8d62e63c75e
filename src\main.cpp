// INCLUDE FILE
// storage (moved to top to avoid conflicts)
#include "FS.h"
#include <LittleFS.h>
#include <Preferences.h>

// main
#include <OneButton.h>
#include <main.h>
#include "main_include.h"
#include "esp_coexist.h"

// LittleFS is now provided by the built-in ESP32 Arduino framework

// wifi
#include <ArduinoJson.h>
#include <HTTPClient.h>
#include <HttpsOTAUpdate.h>
#include <NTPClient.h>
#include <PubSubClient.h>
#include <WiFi.h>
#include <PsychicMqttClient.h>
#include <WiFiClientSecure.h>
#include <memory>
#include <algorithm>

// ble
#include <NimBLEDevice.h>
#include <ble_server\ble_server.h>

// lvgl
#include <TFT_eSPI.h>
#include <lvgl.h>
// #include <lvgl\ui.h>
// #include <lvgl\ui_helpers.h>
#include "lvgl\generated\gui_guider.h"
#include "lvgl\custom\custom.h"
#include "lvgl\generated\widgets_init.h"
#include "lv_port_indev.h"
#include "certificate.h"

// audio
#include <Audio.h>
#include <mutex>
#include <queue>

// HASH
#include <unordered_map>
#include <functional>

#include "mbedtls/md.h"
#include <esp_crt_bundle.h>

// VARIABLE
// system
char DeviceUid[14] = {};
OneButton KeyExit(KEY_EXIT, false, false);
OneButton KeyChange(KEY_CHANGE, true);

//***存放用户信息的数组
UserInfo userInfo[MAX_USER_NUMBER] = {};
uint8_t userNumbers = 0;
uint8_t currentUserIndex = 0;

//***存放ble信任列表
BleTrustInfo bleTrustInfo[MAX_BLE_TRUST_NUMBER] = {};
uint8_t bleTrustNumber = 0;

//***存放wifi数据的数组
std::array<WifiData, MAX_WIFI_NUMBER> wifiInfo;
std::atomic<uint8_t> wifiStorageCnt{0};

//***存放未读信息的数组
MessageEvent messageEvent[MAX_MESSAGE_NUMBER] = {};
uint8_t message_len[MAX_MESSAGE_NUMBER] = {0};
uint8_t messageNumber = 0;
// 弹窗信息显示的时间 /s
int32_t messageDisplayTime = 0;
// message文件所占的长度
uint32_t messageFileLength = 0;

//***存放last温度信息
uint16_t temperaturedLastData = 0;

// 电压测量:IO0
#define POWER_DETECT_IO BAT_VOL
#define POWER_CHARGE_IO CHRG
// int voltage_data = 0;

// 背光控制
#define LCD_BGLIGHT LCD_CTR
// 息屏或者亮屏状态
uint8_t system_state = true;

uint32_t last_server_msg_time = 0; // 最后一次接收服务器消息的时间
uint32_t last_ble_msg_time = 0;    // 最后一次接收蓝牙广播的时间
uint32_t last_key_press_time = 0;  // 最后一次按键操作的时间

// ota
extern const uint8_t rootca_crt_bundle_start[] asm("_binary_src_certs_x509_crt_bundle_bin_start");
extern const uint8_t rootca_crt_bundle_end[] asm("_binary_src_certs_x509_crt_bundle_bin_end");
HttpsOTAStatus_t otastatus;
String otaUrl = "http://bin.bemfa.com/b/1BcMjkyOWY0MWZlYzNlNDQ2Nzk5MDcxNWE4Y2NjNDNkMjA=ESP32OTA.bin";

String FirmwareVersion = "3.0.3"; // 每次生成新固件的时候注意修改版本号
String ServerFwVersion = "-.-.-";
uint8_t otaFlag = 0;
uint8_t restart_flag = 0;
uint8_t restart_cnt = 0;

// storage
// 存放配置的地址
String wifi_path = "/config/wifi.txt";
String account_path = "/config/account.txt";
String ble_info_path = "/config/bleInfo.txt";
String audio_config_path = "/config/audioConfig.txt";
String sleep_config_path = "/config/sleepConfig.txt";
// 存放数据的地址
String message_path = "/data/message.txt";
String offline_data_path = "/data/offline.txt";
String last_data_path[MAX_USER_NUMBER] = {
    "/data/last/patient_0.txt",
    "/data/last/patient_1.txt",
    "/data/last/patient_2.txt",
    "/data/last/patient_3.txt",
    "/data/last/patient_4.txt",
    "/data/last/patient_5.txt",
    "/data/last/patient_6.txt",
    "/data/last/patient_7.txt",
    "/data/last/patient_8.txt",
    "/data/last/patient_9.txt"};

String wav_file_path_base = "https://d.gto.so/gateway/voice/";
String wav_file_path[10] = {
    "open_app_to_config",
    "network_success",
    "select_user",
    "blood_pressure_data",
    "temperature_data",
    "weight_data",
    "blood_glucose_data",
    "blood_oxygen_data",
    "tap_smart_config",
    "new_message"};

uint8_t wav_exist_flag[10] = {false, false, false, false, false, false, false, false, false, false};

// 存放用户头像的地址
String user_img_file_path = "/data/img";
String user_img_path[MAX_USER_NUMBER] = {};

uint8_t offline_data_send_flag = 0;
// 存放音频的地址
String audio_path = "/audio";
Audio audio;
uint8_t audio_enable_flag = 1;
uint8_t audio_volume = 21;

/*uid*/
// 芯片id
Preferences preferences;
uint32_t PrefChipId;

// wifi
PsychicMqttClient client;
// HTTPClient http;
WiFiUDP ntpUDP;
WiFiClientSecure clientSecure(true);
int8_t TimeZone = 8;
NTPClient timeClient(ntpUDP, "ntp.aliyun.com", TimeZone * 3600, 60000);
String SubTopicHeader[6] = {"binding/", "event/", "config/", "ota/", "devices/", "gatewayBinding/"};
String DefaultSubTopic = "devices/";
String DefaultSub = "/provision";
uint8_t wifi_connect_tick = 0;    // wifi尝试连接时间
uint8_t wifi_reconnect_count = 0; // wifi尝试连接次数
// ssl服务器连接相关变量
static uint8_t ssl_connect_status = 0xFF; // 0:失败; 1:成功; 0xFF:连接中
static TaskHandle_t ssl_connect_task = NULL;

// MQTT服务器连接相关变量
static uint8_t mqtt_connect_status = 0x00; // 0:失败; 1:成功;
static uint8_t mqtt_reconnect_flag = 0;
static TaskHandle_t mqtt_connect_task = NULL;

// static TaskHandle_t wifi_handler_task = NULL;

// 存放消息的数组
// S_MESSAGE_BOX message[MAX_MESSAGE_NUMBER] = {};
// mqtt服务器相关设置
const char *MqttServer = "mqtts://stage.fondcircle.com:8883";
uint16_t ServerPort = 8883;
const char *wss_server = "wss://emqx.fondcircle.com:443/mqtt";
char ClientId[14] = {};
std::string MqttUser = "b0:be:83:0d:38";
std::string MqttPass = "fond_circle_gateway";
std::string DefaultMqttUser = "default_device_user";
std::string DefaultMqttPass = "default_device_pwd";
char key_result[15] = {};
uint8_t device_first_binding_flag = 1; // 初始为1，表示使用默认账号
// host name
const char *HostName = "Fond Circle Gateway";
// 网络状态以及服务器连接状态
// uint8_t WifiState = 0;   // 0:disconnect,1:connected,2:connecting,3:smartconfig,4:first connect
// uint8_t ServerState = 0; // 0:disconnect,1:connected
// uint8_t MqttState = 0;

// 在全局变量区域添加
SemaphoreHandle_t xWifiMutex = NULL;
SemaphoreHandle_t xUserInfoMutex = NULL;
SemaphoreHandle_t xMqttStatusMutex = NULL;
SemaphoreHandle_t xWifiInfoMutex = NULL;
portMUX_TYPE wifiStateMux = portMUX_INITIALIZER_UNLOCKED;

// 将原来的状态变量替换为原子类型
std::atomic<E_WifiSta> WifiState(WIFI_DISCONNECTED);
std::atomic<E_ServerSta> ServerState(SERVER_DISCONNECTED);
std::atomic<E_MqttSta> MqttState(MQTT_CLIENT_DISCONNECTED);
std::atomic<E_WifiScanSta> multi_connect_flag(WIFI_SCAN_DEFAULT);

uint8_t binding_state = 1;
uint8_t recovery_state = 1;
int8_t if_rcv_binding_sta = -1; // -1:default; 0:unbinging; 1:binding;

// ble
#define RSSI_THRESHOLD -70

BLEScan *pBLEScan;
// 需要扫描的蓝牙设备名
const char *TemperatureDevice_A = "AOJ-20A";
const char *TemperatureDevice_B = "FTM-D1";
const char *BloodPressureDevice_A = "AOJ-30B";
const char *BloodPressureDevice_B = "FBP-A1";
const char *BloodOxygenDevice_A = "AOJ-70A";
const char *BloodOxygenDevice_B = "AOJ-70B";
const char *BloodOxygenDevice_C = "FPO-F1";
const char *WeighingScaleDevice_A = "LFScale";
const char *WeighingScaleDevice_B = "Health Scale";
const char *WeighingScaleDevice_C = "FBF-F1";
const char *BloodGlucoseDevice_A = "FC-GLU";
const char *BloodGlucoseDevice_B = "FBG-M1";

// 定义设备处理函数的类型
using DeviceHandler = std::function<void(NimBLEAdvertisedDevice *, const S_TrustListCheck &)>;
// 创建设备名称到处理函数的映射
std::unordered_map<std::string, DeviceHandler> deviceHandlers;

// 存放蓝牙数据的变量
S_VitalsData ble_disp_data;
S_VitalsData ble_store_data[MAX_USER_NUMBER] = {};
S_BloodGlucoseAdv glu_data;
// 体脂秤传过来的单位
String weight_unit[12] = {"KG", "LB", "STLB", "JIN", "G", "LB_OZ", "OZ", "ML_WATER", "ML_MILK", "FL_OZ_WATER", "FL_OZ_MILK", "ST"};
// 蓝牙状态标志位 0：无操作；1：进行wifi连接；2：进行蓝牙广播；3：进行蓝牙扫描
uint8_t bleStateFlag = 0;

#define BLE_SCAN_TIME 300
#define BLE_RESTART_TIME 3
int16_t bleNoDataTimeCount = BLE_SCAN_TIME;
int16_t bleRestartScanTimeCount = 0;

// lvgl
// 显示相关设置
static const uint16_t screenWidth = 320;
static const uint16_t screenHeight = 240;
static lv_disp_draw_buf_t draw_buf;
static lv_color_t *buf1;
static lv_color_t *buf2;
TFT_eSPI tft = TFT_eSPI(screenWidth, screenHeight); /* TFT instance */
extern struct page *cur_page;

// 交互相关
uint16_t account_cnt = 0;

uint8_t pre_system_state = 0;

// 弹窗显示时间
int8_t pop_display_time = 0;

uint8_t blood_glucose_submit_flag = 0; // 0:表示目前上传的数据并不是血糖数据；1:表示目前上传的数据是血糖数据
std::string generateHMAC(const std::string &message, const std::string &key);

// static TaskHandle_t lvgl_task_handle = NULL;

TaskHandle_t audioPlayTask = NULL;
uint8_t msg_flag = 0;

void checkResetReason();
void bleClearResults();
void mqttClientDisconnect();
void audio_prompt(const char *filename);
void voice_system_status_report(void);
// FUNCTION
/************************************ system****************************************************/
void cleanUid(void)
{
    preferences.begin("chipId", false);
    preferences.remove("id");
    preferences.end();
}

void getUid(void)
{
    log_i("getUid");
    uint32_t chipId;
    // char charKey[30] = {};
    preferences.begin("chipId", false);
    PrefChipId = preferences.getULong("id", 0);
    preferences.end();
    if (PrefChipId == 0)
    {
        chipId = esp_random();
        preferences.begin("chipId", false);
        preferences.putULong("id", chipId);
        preferences.end();
    }
    else
    {
        chipId = PrefChipId;
    }
    DeviceUid[0] = 'f';
    DeviceUid[1] = 'f';
    ClientId[0] = 'f';
    ClientId[1] = 'f';

    for (int i = 0; i < 4; i++)
    {
        sprintf(DeviceUid + 3 * i + 2, ":%02x", (chipId >> (i * 8)) & 0xff);
        sprintf(ClientId + 3 * i + 2, ":%02x", (chipId >> (i * 8)) & 0xff);
    }
    // 替换mqtt服务器的登录用户id
    MqttUser.replace(0, sizeof(DeviceUid) / sizeof(DeviceUid[0]), DeviceUid);
    log_i("Uid = %s\n", DeviceUid);

    MqttPass = generateHMAC("healthhub", DeviceUid);
    log_i("New MqttPass = %s", MqttPass.c_str());
}

std::string generateHMAC(const std::string &message, const std::string &key)
{
    unsigned char hmac[32]; // SHA-256 HMAC 输出长度为 32 字节

    mbedtls_md_context_t ctx;
    mbedtls_md_type_t md_type = MBEDTLS_MD_SHA256;

    mbedtls_md_init(&ctx);
    mbedtls_md_setup(&ctx, mbedtls_md_info_from_type(md_type), 1);
    mbedtls_md_hmac_starts(&ctx, (const unsigned char *)key.c_str(), key.length());
    mbedtls_md_hmac_update(&ctx, (const unsigned char *)message.c_str(), message.length());
    mbedtls_md_hmac_finish(&ctx, hmac);
    mbedtls_md_free(&ctx);

    // 将 HMAC 转换为十六进制字符串，但只取前 15 个字符
    char hexHMAC[16]; // 15 个字符 + 1 个结束符
    for (int i = 0; i < 7; i++)
    { // 只需要 7 个字节就能生成 14 个十六进制字符
        sprintf(hexHMAC + (i * 2), "%02x", hmac[i]);
    }
    sprintf(hexHMAC + 14, "%01x", hmac[7] >> 4); // 取第 8 个字节的高 4 位作为最后一个字符
    hexHMAC[15] = 0;                             // 添加字符串结束符

    return std::string(hexHMAC);
}

// 获取一个数的百为十位个位和小数点后一位的数字
uint8_t getHundred(uint16_t value)
{
    return value / 100;
}
uint8_t getTen(uint16_t value)
{
    return (value / 10) % 10;
}
uint8_t getOne(uint16_t value)
{
    return value % 10;
}
uint8_t getDecimal(float value)
{
    return (uint16_t)(value * 10) % 10;
}
// 串口初始化
void serial_init()
{
    Serial.begin(115200);
}

// 按键初始化
void button_init()
{
    log_i("button_init");
    /*返回按键*/
    KeyExit.attachClick(user_exit_press_event_handler);
    KeyExit.attachLongPressStart(user_exit_long_press_event_handler);

    /*用户切换按键*/
    KeyChange.attachClick(user_change_press_event_handler);
    KeyChange.attachLongPressStart(user_change_long_press_event_handler);
}
// 背光控制引脚初始化
void lcd_bg_control_init()
{
    log_i("bg_light_init");
    pinMode(LCD_BGLIGHT, OUTPUT);
}

void setWifiInfo(uint8_t index, const String &ssid, const String &psw)
{
    if (xSemaphoreTake(xWifiInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        if (index < MAX_WIFI_NUMBER)
        {
            wifiInfo[index].ssid = ssid;
            wifiInfo[index].psw = psw;
        }
        xSemaphoreGive(xWifiInfoMutex);
    }
}

WifiData getWifiInfo(uint8_t index)
{
    WifiData temp_info;
    if (xSemaphoreTake(xWifiInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        temp_info = wifiInfo[index];
        xSemaphoreGive(xWifiInfoMutex);
    }
    if (index < MAX_WIFI_NUMBER)
    {
        return temp_info;
    }
    return WifiData{};
}

uint8_t getWifiStorageCount()
{
    return wifiStorageCnt.load();
}

void setWifiStorageCount(uint8_t count)
{
    wifiStorageCnt.store(count);
}

void incrementWifiStorageCount()
{
    uint8_t current = wifiStorageCnt.load();
    if (current < MAX_WIFI_NUMBER)
    {
        wifiStorageCnt++;
    }
}

void decrementWifiStorageCount()
{
    uint8_t current = wifiStorageCnt.load();
    if (current > 0)
    {
        wifiStorageCnt--;
    }
}

void clearWifiStorage()
{
    for (auto &wifi : wifiInfo)
    {
        wifi.ssid = "";
        wifi.psw = "";
    }
    wifiStorageCnt.store(0);
}

void WifiArrayInit()
{
    for (auto &wifi : wifiInfo)
    {
        wifi.ssid = "";
        wifi.psw = "";
    }
}
// 数据储存初始化
void storage_init()
{
    log_i("storage_init");
    LittleFS.begin(true, BASE_FILE_PATH);
    dirAndFileCreate();
}
// 按键控制
void lcd_bg_control(bool state)
{
    digitalWrite(LCD_BGLIGHT, state == false ? 1 : 0);
}

void power_init()
{
    pinMode(KEY_POWER_ON, OUTPUT);
}

// 按键控制
void power_on(bool state)
{
    digitalWrite(KEY_POWER_ON, state);
}

void bg_awake(void)
{
    uint32_t current_time = millis() / 1000;
    // log_i("awake");
    if (system_state == false)
        system_state = true;

    last_server_msg_time = current_time;
    last_ble_msg_time = current_time;
    last_key_press_time = current_time;
}

// 长按退出键关闭屏幕背光
void user_exit_long_press_event_handler(void)
{
    static unsigned long last_press_time = 0;
    unsigned long current_time = millis();

    // 防抖处理：忽略300ms内的连续按键，但允许系统启动后的第一次按键
    if (last_press_time != 0 && (current_time - last_press_time) < 300)
    {
        return;
    }
    last_press_time = current_time;

    if (system_state == true)
    {
        system_state = false;
        power_on(false);
        lcd_bg_control(LIGHT_OFF);
        log_i("long exit btn: power off");
    }
}

void sleep_screen_handler()
{
    if (WifiState.load() == WIFI_SMARTCONFIG ||
        WifiState.load() == WIFI_FIRST_CONNECTED)
    {
        return;
    }

    uint8_t sleep_time = info_modify_SleepScreen_get_select();
    uint32_t sleep_timeout = 0;

    // 根据设置确定超时时间(秒)
    switch (sleep_time)
    {
    case 0:
        sleep_timeout = 300;
        break; // 5分钟
    case 1:
        sleep_timeout = 900;
        break; // 15分钟
    case 2:
        sleep_timeout = 1800;
        break; // 30分钟
    case 3:
        return; // 不休眠
    default:
        sleep_timeout = 300;
        break;
    }

    uint32_t current_time = millis() / 1000; // 转换为秒

    // 检查三个条件的超时情况
    bool server_timeout = (current_time - last_server_msg_time) > sleep_timeout;
    bool ble_timeout = (current_time - last_ble_msg_time) > sleep_timeout;
    bool key_timeout = (current_time - last_key_press_time) > sleep_timeout;

    // 所有条件都超时才进入休眠
    if (server_timeout && ble_timeout && key_timeout)
    {
        system_state = false;
    }
}
const uint8_t KEY_PINS[5] = {KEY_UP, KEY_DOWN, KEY_ENTER, KEY_EXIT, KEY_CHANGE};
bool button_change_check(void)
{
    static uint8_t button_state[5] = {0}; // 0: 未按下, 1: 按下, 2: 等待释放
    // static uint8_t pre_button_state[5] = {0}; // 0: 未按下, 1: 按下
    bool state_changed = false;

    for (int i = 0; i < 5; i++)
    {
        uint8_t current_state = digitalRead(KEY_PINS[i]);

        if (current_state == LOW && button_state[i] == 0) // 按键被按下
        {
            button_state[i] = 1;
        }
        else if (current_state == HIGH && button_state[i] == 1) // 按键被释放
        {
            button_state[i] = 0;
            state_changed = true;
            system_state = true; // 唤醒屏幕
            last_key_press_time = millis() / 1000;
            log_i("Button %d pressed, waking up screen", i);
            break; // 一旦检测到按键释放，立即退出循环
        }
    }

    return state_changed;
}

// 获取时间+日期
String getUTCFormatterDate()
{
    time_t rawTime = timeClient.getEpochTime() - TimeZone * 3600;
    char s[20];
    strftime(s, 20, "%Y/%m/%d %H:%M:%S", gmtime(&rawTime));
    return String(s);
}

// 获取时间+日期
String getFormatterDate()
{
    time_t rawTime = timeClient.getEpochTime();
    char s[20];
    strftime(s, 20, "%H:%M %Y/%m/%d", localtime(&rawTime));
    return String(s);
}

// 获取时间
String getFormatterTime()
{
    time_t rawTime = timeClient.getEpochTime();
    char s[6];
    strftime(s, 6, "%H:%M", localtime(&rawTime));
    return String(s);
}

// 用户切换
void user_change_press_event_handler(void)
{
    if (system_state == false || otaFlag == 1)
    {
        return;
    }

    log_i("cur_page: %s", get_cur_page()->name);
    // 在主页时，不响应切换按键
    if (get_cur_page()->body_obj == Home)
    {
        return;
    }

    uint8_t index = member_get_index_scr_Family();
    log_i("index: %d", index);

    if (get_cur_page()->body_obj != Family)
    {
        // 安全地管理页面栈：先创建新栈，再跳转页面
        // 这样即使页面跳转失败，也不会留下悬空指针
        delete_page_stack();    // 删除旧栈
        re_create_page_stack(); // 立即创建新栈

        // 执行页面跳转
        page_transition("Family");
    }
    else
    {
        index++;
        member_set_index_scr_Family(index);
        member_focus_scr_Family(member_get_index_scr_Family());
    }
}
// 长按切换按键
void user_change_long_press_event_handler(void)
{
}

uint8_t info_modify_DataDisplay_Pop(char *tempdata, char *time)
{
    uint8_t if_refresh_pop = 0;
    if (get_cur_page()->body_obj != Vitals && get_cur_page()->page_level >= 3)
    {
        if (blood_glucose_submit_flag == 0)
        {
            S_DataDisplayInfo pop_info;
            pop_info.info = new char[strlen(tempdata) + 1];
            strcpy(pop_info.info, tempdata);
            pop_info.member = new char[userInfo[currentUserIndex].name.length() + 1];
            strcpy(pop_info.member, userInfo[currentUserIndex].name.c_str());
            strcpy(pop_info.time, time);
            info_modify_DataDisplay(pop_info);
            if (get_cur_pop() == NULL)
            {
                log_i("pop null");
                pop_on("DataDisplay");
                if_refresh_pop = 0;
            }
            else
            {
                log_i("pop refresh");
                info_refresh_DataDisplay();
                if_refresh_pop = 1;
            }

            pop_display_time = POP_DISPLAY_TIME;
            delete (pop_info.info);
            delete (pop_info.member);
        }
        else if (blood_glucose_submit_flag == 1)
        {
            S_GlucoseDisplayInfo pop_info;
            pop_info.info = new char[strlen(tempdata) + 1];
            strcpy(pop_info.info, tempdata);
            pop_info.member = new char[userInfo[currentUserIndex].name.length() + 1];
            strcpy(pop_info.member, userInfo[currentUserIndex].name.c_str());
            strcpy(pop_info.time, time);
            info_modify_GlucoseDisplay(pop_info);
            if (get_cur_pop() == NULL)
            {
                log_i("pop null");
                pop_on("GlucoseDisplay");
                if_refresh_pop = 0;
            }

            pop_display_time = POP_DISPLAY_LONG_TIME;
            delete (pop_info.info);
            delete (pop_info.member);
        }
    }
    return if_refresh_pop;
}

// 更新当前固件版本
void versionUpdataToLabel(void)
{
    String server_fw_display = "V" + ServerFwVersion;
    String fw_display = "V" + FirmwareVersion;

    S_VersionInfo info;
    info.server = new char[server_fw_display.length() + 1];
    info.current = new char[fw_display.length() + 1];
    strcpy(info.server, server_fw_display.c_str());
    strcpy(info.current, fw_display.c_str());
    if (version_compare() > 0)
    {
        info.update_state = 1;
    }
    else
    {
        info.update_state = 0;
    }
    info_modify_Version(info);
    delete info.server;
    delete info.current;
}
// adc引脚初始化
void power_detect_init()
{
    log_i("power_detect_init");
    analogReadResolution(12); // 12bit 0-4096
    pinMode(POWER_CHARGE_IO, INPUT_PULLUP);
}
// 获取电池电量的adc值
int get_power()
{
    return analogRead(POWER_DETECT_IO);
}
// 版本号对比
int8_t version_compare()
{
    int major1 = 0, minor1 = 0, patch1 = 0;
    int major2 = 0, minor2 = 0, patch2 = 0;

    // 将版本号字符串按照"."进行分割
    sscanf(FirmwareVersion.c_str(), "%d.%d.%d", &major1, &minor1, &patch1);
    sscanf(ServerFwVersion.c_str(), "%d.%d.%d", &major2, &minor2, &patch2);

    if (major1 != major2)
    {
        return (major1 < major2) ? 1 : -1;
    }
    else if (minor1 != minor2)
    {
        return (minor1 < minor2) ? 1 : -1;
    }
    else if (patch1 != patch2)
    {
        return (patch1 < patch2) ? 1 : -1;
    }
    return 0;
}

static void ble_disp_data_init()
{
    ble_disp_data.oxygen.SPO2 = 0x00;
    ble_disp_data.oxygen.PulseRate = 0x00;
    ble_disp_data.oxygen.PIData = 0x00;
    strcpy(ble_disp_data.oxygen.time, "12:00 2022/01/01");
    ble_disp_data.pressure.Systolic = 0x00;
    ble_disp_data.pressure.Diastolic = 0x00;
    ble_disp_data.pressure.Pulse = 0x00;
    strcpy(ble_disp_data.pressure.time, "12:00 2022/01/01");
    ble_disp_data.weight.WeightH = 0x00;
    ble_disp_data.weight.WeightL = 0x00;
    ble_disp_data.weight.Unit = KG;
    ble_disp_data.weight.ImpedanceH = 0x00;
    ble_disp_data.weight.ImpedanceM = 0x00;
    ble_disp_data.weight.ImpedanceL = 0x00;
    strcpy(ble_disp_data.weight.time, "12:00 2022/01/01");
    ble_disp_data.temperature.TempH = 0x00;
    ble_disp_data.temperature.TempL = 0x00;
    strcpy(ble_disp_data.temperature.time, "12:00 2022/01/01");
    ble_disp_data.glucose.Glucose = 0x00;
    ble_disp_data.glucose.mealState = 0x01;
    strcpy(ble_disp_data.glucose.time, "12:00 2022/01/01");
}

static void disp_data_set(S_OxygenInfo oxy_info, S_PressureInfo pres_info, S_WeightInfo weig_info, S_TemperatureInfo temp_info, S_BloodGlucoseInfo gluco_info)
{
    info_modify_Vitals_Oxygen(oxy_info);
    info_modify_Vitals_Pressure(pres_info);
    info_modify_Vitals_Weight(weig_info);
    info_modify_Vitals_Temperature(temp_info);
    info_modify_Vitals_BloodGlucose(gluco_info);
}

void disp_data_set_with_store(void)
{
    // 添加安全检查
    if (currentUserIndex >= MAX_USER_NUMBER)
    {
        log_e("Invalid user index: %d", currentUserIndex);
        return;
    }

    /*restore oxygen data*/
    S_OxygenInfo oxy_info = {};

    if (ble_store_data[currentUserIndex].oxygen.SPO2 != 0x7f)
    {
        oxy_info.so2 = ble_store_data[currentUserIndex].oxygen.SPO2;
    }
    if (ble_store_data[currentUserIndex].oxygen.PulseRate != 0xff)
    {
        oxy_info.pr = ble_store_data[currentUserIndex].oxygen.PulseRate;
    }
    oxy_info.pi = ble_store_data[currentUserIndex].oxygen.PIData;
    if (ble_store_data[currentUserIndex].oxygen.time[0] != '\0')
    {
        strncpy(oxy_info.time, ble_store_data[currentUserIndex].oxygen.time, sizeof(oxy_info.time) - 1);
        oxy_info.time[sizeof(oxy_info.time) - 1] = '\0';
    }
    else
    {
        snprintf(oxy_info.time, sizeof(oxy_info.time), "12:00 2022/01/01");
    }
    /*restore pressure data*/
    S_PressureInfo pres_info = {};
    pres_info.sys = ble_store_data[currentUserIndex].pressure.Systolic;
    pres_info.dia = ble_store_data[currentUserIndex].pressure.Diastolic;
    pres_info.pul = ble_store_data[currentUserIndex].pressure.Pulse;
    if (ble_store_data[currentUserIndex].pressure.time[0] != '\0')
    {
        strncpy(pres_info.time, ble_store_data[currentUserIndex].pressure.time, sizeof(pres_info.time) - 1);
        pres_info.time[sizeof(pres_info.time) - 1] = '\0';
    }
    else
    {
        snprintf(pres_info.time, sizeof(pres_info.time), "12:00 2022/01/01");
    }
    /*restore weight data*/
    S_WeightInfo weig_info = {};
    weig_info.weight = (ble_store_data[currentUserIndex].weight.WeightH << 8) | ble_store_data[currentUserIndex].weight.WeightL;
    if (ble_store_data[currentUserIndex].weight.time[0] != '\0')
    {
        strncpy(weig_info.time, ble_store_data[currentUserIndex].weight.time, sizeof(weig_info.time) - 1);
        weig_info.time[sizeof(weig_info.time) - 1] = '\0';
    }
    else
    {
        snprintf(weig_info.time, sizeof(weig_info.time), "12:00 2022/01/01");
    }
    /*restore temperature data*/
    S_TemperatureInfo temp_info = {};
    temp_info.temperature = (ble_store_data[currentUserIndex].temperature.TempH << 8) | ble_store_data[currentUserIndex].temperature.TempL;
    if (ble_store_data[currentUserIndex].temperature.time[0] != '\0')
    {
        strncpy(temp_info.time, ble_store_data[currentUserIndex].temperature.time, sizeof(temp_info.time) - 1);
        temp_info.time[sizeof(temp_info.time) - 1] = '\0';
    }
    else
    {
        snprintf(temp_info.time, sizeof(temp_info.time), "12:00 2022/01/01");
    }

    /*restore blood glucose data*/
    S_BloodGlucoseInfo glucose_info = {};
    glucose_info.bloodGlucose = ble_store_data[currentUserIndex].glucose.Glucose;
    glucose_info.mealState = ble_store_data[currentUserIndex].glucose.mealState;
    if (ble_store_data[currentUserIndex].glucose.time[0] != '\0')
    {
        strncpy(glucose_info.time, ble_store_data[currentUserIndex].glucose.time, sizeof(glucose_info.time) - 1);
        glucose_info.time[sizeof(glucose_info.time) - 1] = '\0';
    }
    else
    {
        snprintf(glucose_info.time, sizeof(glucose_info.time), "12:00 2022/01/01");
    }

    disp_data_set(oxy_info, pres_info, weig_info, temp_info, glucose_info);
}

void last_operation_time_init(void)
{
    log_i("last_operation_init");
    last_server_msg_time = millis() / 1000;
    last_ble_msg_time = millis() / 1000;
    last_key_press_time = millis() / 1000;
}
// 总初始化函数
void system_init()
{
    serial_init(); // 串口初始化
    checkResetReason();
    storage_init();
    getUid(); // 获取mac地址 FF:XX:XX:XX:XX
    button_init();
    wifi_mqtt_topic_create();
    power_detect_init();   // 电池电量检测
    lcd_bg_control_init(); // 背光控制
    last_operation_time_init();

    // 初始化语音混合系统
    voice_error_t voice_err = voice_hybrid_init();
    if (voice_err == VOICE_ERR_OK)
    {
        log_i("Voice hybrid system initialized successfully");

        // 配置语音系统
        voice_set_priority_policy(VOICE_PRIORITY_SPEED_FIRST);
        voice_set_cache_size(64); // 64KB缓存
        voice_set_compression_enabled(true);

        // 配置日志系统
        voice_configure_logging(true, true, 100 * 1024, ESP_LOG_INFO);

        // 预加载常用语音文件
        voice_preload("open_app_to_config");
        voice_preload("network_success");
        voice_preload("select_user");

        // 获取存储状态
        voice_storage_info_t storage_info;
        if (voice_get_storage_info(&storage_info) == VOICE_ERR_OK)
        {
            log_i("Voice storage: ROM %d/%d bytes, LittleFS %d/%d bytes",
                  storage_info.rom_used_size, storage_info.rom_total_size,
                  storage_info.littlefs_used_size, storage_info.littlefs_total_size);
        }

        // 延迟执行状态报告，让系统完全初始化
        xTaskCreatePinnedToCore([](void *param)
                                {
            vTaskDelay(pdMS_TO_TICKS(5000)); // 等待5秒
            voice_system_status_report();
            vTaskDelete(NULL); }, "VoiceStatusReport", 4096, NULL, 1, NULL, 0);
    }
    else
    {
        log_e("Failed to initialize voice hybrid system: %d", voice_err);
    }
}

void restore_data()
{
    // 读取最后一次储存的数据
    bool read_state = lastDataRead();
    if (read_state)
    {
        log_i("so2 = %d, pr = %d, pi = %d, time=%s\n",
              ble_disp_data.oxygen.SPO2,
              ble_disp_data.oxygen.PulseRate,
              ble_disp_data.oxygen.PIData,
              ble_disp_data.oxygen.time);
        log_i("sys = %d, dia = %d, pul = %d, time=%s\n",
              ble_disp_data.pressure.Systolic,
              ble_disp_data.pressure.Diastolic,
              ble_disp_data.pressure.Pulse,
              ble_disp_data.pressure.time);
        log_i("weight = %d, impedance = %d, time=%s\n",
              (ble_disp_data.weight.WeightH << 8) | ble_disp_data.weight.WeightL,
              (ble_disp_data.weight.ImpedanceH << 16) | (ble_disp_data.weight.ImpedanceM << 8) | ble_disp_data.weight.ImpedanceL,
              ble_disp_data.weight.time);
        log_i("temperature = %d, time = %s\n",
              ble_disp_data.temperature.TempH << 8 | ble_disp_data.temperature.TempL,
              ble_disp_data.temperature.time);

        log_i("glucose = %d, mealState = %d, time = %s\n",
              ble_disp_data.glucose.Glucose,
              ble_disp_data.glucose.mealState,
              ble_disp_data.glucose.time);
    }
    else
    {
        ble_disp_data_init();
    }
    /*restore oxygen data*/
    S_OxygenInfo oxy_info;

    if (ble_disp_data.oxygen.SPO2 != 0x7f)
    {
        oxy_info.so2 = ble_disp_data.oxygen.SPO2;
    }
    if (ble_disp_data.oxygen.PulseRate != 0xff)
    {
        oxy_info.pr = ble_disp_data.oxygen.PulseRate;
    }
    oxy_info.pi = ble_disp_data.oxygen.PIData;
    strcpy(oxy_info.time, ble_disp_data.oxygen.time);
    /*restore pressure data*/
    S_PressureInfo pres_info;
    pres_info.sys = ble_disp_data.pressure.Systolic;
    pres_info.dia = ble_disp_data.pressure.Diastolic;
    pres_info.pul = ble_disp_data.pressure.Pulse;
    strcpy(pres_info.time, ble_disp_data.pressure.time);
    /*restore weight data*/
    S_WeightInfo weig_info;
    weig_info.weight = (ble_disp_data.weight.WeightH << 8) | ble_disp_data.weight.WeightL;
    strcpy(weig_info.time, ble_disp_data.weight.time);
    /*restore temperature data*/
    S_TemperatureInfo temp_info;
    temp_info.temperature = (ble_disp_data.temperature.TempH << 8) | ble_disp_data.temperature.TempL;
    strcpy(temp_info.time, ble_disp_data.temperature.time);

    /*restore temperature data*/
    S_BloodGlucoseInfo glucose_info;
    glucose_info.bloodGlucose = ble_disp_data.glucose.Glucose;
    glucose_info.mealState = ble_disp_data.glucose.mealState;
    strcpy(glucose_info.time, ble_disp_data.glucose.time);
    log_i("meal_state: %d", glucose_info.mealState);
    disp_data_set(oxy_info, pres_info, weig_info, temp_info, glucose_info);

    // 读取用户信息
    accountDataRead();
    userNumbers = account_cnt; // Set user count from account data

    // 根据存储状态设置设备绑定标志
    if (userNumbers > 0)
    {
        device_first_binding_flag = 0; // 如果已存储用户，使用正式账号
    }

    // 读取ble信任列表信息
    bleInfoDataRead();

    // 读取语音的配置
    audioConfigRead();

    // 读取休眠的配置
    sleepConfigRead();

    // 读取消息数据
    read_state = messageDataRead();
    if (read_state)
    {
        messageDisplayAll();
    }

    read_state = get_store_wav_file();
    if (!read_state)
    {
    }
}

TaskHandle_t WavTask;

void WavTask_func(void *pvParameters)
{
    log_i("WavTask_func starting");

    // 检查wav_file_path是否已初始化
    for (int i = 0; i < 10; i++)
    {
        if (wav_file_path[i].length() == 0)
        {
            log_e("wav_file_path[%d] is empty", i);
            continue;
        }

        if (!wav_exist_flag[i])
        {
            log_i("Downloading file: %s", wav_file_path[i].c_str());
            try
            {
                getFile(wav_file_path[i].c_str());
            }
            catch (const std::exception &e)
            {
                log_e("Exception in getFile: %s", e.what());
            }
            catch (...)
            {
                log_e("Unknown exception in getFile");
            }
        }
    }

    log_i("WavTask_func completed");
    // 下载完成之后删除任务
    WavTask = NULL;    // 清除任务句柄
    vTaskDelete(NULL); // 删除当前任务
}

void re_download_wav_file(void)
{
    static uint8_t download_flag = 0;
    if (download_flag == 0)
    {
        download_flag = 1;
        BaseType_t result = xTaskCreatePinnedToCore(
            WavTask_func, /* Task function. */
            "WavTask",    /* name of task. */
            5000,         /* Stack size of task */
            NULL,         /* parameter of the task */
            2,            /* priority of the task */
            &WavTask,     /* Task handle to keep track of created task */
            0);

        if (result != pdPASS)
        {
            log_e("Failed to create WavTask, error: %d", result);
            download_flag = 0; // 重置标志以便下次尝试
        }
    }
}
/************************************ ota ****************************************************/
uint32_t total_data;
void OTAEvent(HttpEvent_t *event)
{
    static UIUpdater ui_updater;
    switch (event->event_id)
    {
    case HTTP_EVENT_ERROR:
        bleStateFlag = BLE_SCANNING;
        log_i("Http Event Error");
        if (event->client)
        { // 确保 client 句柄有效
            esp_err_t http_err = esp_http_client_get_errno(event->client);
            log_e("HTTP Client Error: 0x%x (%s)", http_err, esp_err_to_name(http_err));
        }
        else
        {
            log_e("HTTP Client Error: Client handle is NULL");
        }
        // 获取 HTTP 客户端错误码
        log_e("esp_http_client_get_errno: %d (%s)", esp_http_client_get_errno(event->client), esp_err_to_name(esp_http_client_get_errno(event->client)));

        ui_updater.update("Http Event Error");
        break;
    case HTTP_EVENT_ON_CONNECTED:
        log_i("Http Event On Connected");
        ui_updater.update("Http Event On Connected");
        break;
    case HTTP_EVENT_HEADER_SENT:
        log_i("Http Event Header Sent");
        break;
    case HTTP_EVENT_ON_HEADER:
        log_i("Http Event On Header, key=%s, value=%s\n", event->header_key, event->header_value);
        if (!String(event->header_key).compareTo("Content-Length"))
        {
            total_data = String(event->header_value).toInt();
        }
        break;
    case HTTP_EVENT_ON_DATA:
        static uint32_t cur_data_len = 0;
        static uint8_t upload_pct = 0 /**, last_pct = 0*/;
        cur_data_len += event->data_len;
        upload_pct = (uint8_t)(cur_data_len * 100 / total_data);
        log_i("cur_data_len = %d, total_data = %d, upload_pct = %d", cur_data_len, total_data, upload_pct);
        ui_updater.update("fw downloading..", upload_pct);
        break;
    case HTTP_EVENT_ON_FINISH:
        log_i("Http Event On Finish");
        ui_updater.update("Download Finished");
        break;
    case HTTP_EVENT_DISCONNECTED:
        log_i("Http Event Disconnected");
        ui_updater.update("Http Event Disconnected");
        break;
    }
}

// 开始ota时的配置函数
void ota_begin()
{
    otaFlag = 1;
    bleStateFlag = BLE_NO_OPERATION;
    log_i("ota start");
}

// 查看ota进度
void ota_state()
{
    static uint8_t ota_step = 0;
    if (ota_step == 0)
    {
        HttpsOTA.onHttpEvent(OTAEvent);
        // HttpsOTA.begin(otaUrl.c_str(), ota_server_certificate);
        HttpsOTA.begin(otaUrl.c_str(), NULL, false);
        ota_step = 1;
    }
    else
    {
        otastatus = HttpsOTA.status();
        if (otastatus == HTTPS_OTA_SUCCESS)
        {
            restart_flag = 1;
        }
        else if (otastatus == HTTPS_OTA_FAIL)
        {
            otaFlag = 0;
            ota_step = 0;
            log_w("Firmware Upgrade Fail");
            page_transition("Version");
        }
    }
}

void ota_handler()
{
    if (otaFlag == 1)
    {
        ota_state();
    }
    // 升级成功后重启设备
    if (restart_flag == 1)
    {
        if (++restart_cnt >= 3)
        {
            ESP.restart();
        }
    }
    else
    {
        restart_cnt = 0;
    }
}
/************************************ wifi****************************************************/
// String downloadImage(uint8_t index, const char *filename)
// {
//     String filepath;
//     http.begin(userInfo[index].url.c_str());
//     int httpResponseCode = http.GET();
//     if (httpResponseCode > 0)
//     {
//         String contentType = http.header("Content-Type"); // 获取 HTTP 响应的 Content-Type 头部
//         String extension = ".jpg";

//         if (contentType.indexOf("image/png") >= 0)
//         {
//             extension = ".png";
//         }
//         else if (contentType.indexOf("image/jpg") >= 0)
//         {
//             extension = ".jpg";
//         }
//         else if (contentType.indexOf("image/jpeg") >= 0)
//         {
//             extension = ".jpeg";
//         } // 添加其他图片格式的判断

//         filepath = String(filename) + String("/") + String(index) + extension;

//         log_i("filepath :%s", filepath.c_str());

//         File file = LittleFS.open(filepath, "w");
//         if (!file)
//         {
//             Serial.println("Failed to open file for writing");
//             return "";
//         }
//         WiFiClient *stream = http.getStreamPtr();
//         while (stream->available())
//         {
//             file.write(stream->read());
//         }
//         file.close();
//     }
//     else
//     {
//         log_i("HTTP Response code: %d", httpResponseCode);
//     }

//     http.end();

//     return filepath;
// }

// 显示所有消息
void messageDisplayAll()
{
    if (get_cur_page() == NULL)
    {
        return;
    }

    if (get_cur_page()->body_obj == Message)
    {
        for (int i = 0; i < messageNumber; i++)
        {
            message_create_scr_Message(messageNumber, i, messageEvent[i].title.c_str(), messageEvent[i].body.c_str(), messageEvent[i].time.c_str());
        }
    }
}

void debinding_reply(void)
{
    std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));
    String publishServer = "clientId/" + String(str.c_str()) + "/confirmUnbind";
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, "");
    if (pub_result)
    {
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:[]\n\n", pub_result);
}

void recovery_reply(void)
{
    std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));
    String publishServer = "clientId/" + String(str.c_str()) + "/recover";
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, "");
    if (pub_result)
    {
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:[]\n\n", pub_result);
}
// 消息删除函数，在查看完未读消息后删除该消息
uint8_t binding_recv_flag = 0;
void messageDelete(uint8_t index)
{
    messageDataClean();
    log_i("index = %d", index);
    if (messageNumber == 1)
    {
        messageEvent[0].title = "";
        messageEvent[0].body = "";
        messageEvent[0].time = "";
        messageNumber = 0;
        return;
    }

    for (int i = index; i < messageNumber - 1; i++)
    {
        messageEvent[i] = messageEvent[i + 1];
    }
    messageNumber--;
    messageEvent[messageNumber].title = "";
    messageEvent[messageNumber].body = "";
    messageEvent[messageNumber].time = "";

    char data[200] = {0};
    for (int i = 0; i < messageNumber; i++)
    {
        sprintf(data, "%s,%s,%s;", messageEvent[i].title.c_str(), messageEvent[i].body.c_str(), messageEvent[i].time.c_str());
        messageDataWrite(data);
    }
}
// 收到服务器的mqtt消息的处理函数
// void wifi_mqtt_server_set();
void messageReceived(char *topic, char *payload, int retain, int qos, bool dup)
{
    log_i("topic:%s;payload:%s;retain:%d,qos:%d,dup:%d;\n", String(topic).c_str(), String(payload).c_str(), retain, qos, dup);
    last_server_msg_time = millis() / 1000;
    // 检查payload是否为空或过短
    if (payload == NULL || strlen(payload) < 2)
    {
        log_i("Empty or invalid payload received, skipping processing");
        return;
    }

    // 检查payload是否是有效的JSON格式（至少应该以{或[开头）
    if (payload[0] != '{' && payload[0] != '[')
    {
        log_i("Invalid JSON format in payload: %s", payload);
        return;
    }

    if (strlen(payload) > 2048)
    {
        log_i("Payload too large: %d bytes", strlen(payload));
        return;
    }

    DynamicJsonDocument jsonBuffer(2048);
    DeserializationError error = deserializeJson(jsonBuffer, payload);
    if (error)
    {
        log_i("deserializeJson() failed: ");
        log_i("%s\n", error.f_str());
        return;
    }
    char data[200] = {0};

    // blinding command
    if (!String(topic).compareTo(SubTopicHeader[0]))
    {
        // 添加一个string数据用来存储上一次收到的json消息，如果消息与上一次收到的一致，直接跳过
        String last_json_msg = "";
        if (last_json_msg == String(payload))
        {
            return;
        }
        last_json_msg = String(payload);

        // 确保payload是一个JSON数组
        if (!jsonBuffer.is<JsonArray>())
        {
            log_i("Expected JSON array for binding command, but got different format");
            return;
        }

        binding_recv_flag = 1;
        if (account_cnt != 0 || userNumbers != 0)
        {
            member_clean_scr_Family();
            accountDataClean();
            account_cnt = 0;
            userNumbers = 0;
        }
        // 需要添加item是否存在的检测
        for (JsonObject item : jsonBuffer.as<JsonArray>())
        {
            // 检查必要字段是否存在
            if (!item.containsKey("patient_id") || !item.containsKey("patient_type") ||
                !item.containsKey("patient_name") || !item.containsKey("patient_avatar_url"))
            {
                log_i("Missing required fields in binding item");
                continue;
            }

            uint16_t patient_id = item["patient_id"];
            const char *patient_type = item["patient_type"];
            const char *patient_name = item["patient_name"];
            const char *patient_avatar = item["patient_avatar_url"];

            // 检查数组边界
            if (userNumbers >= MAX_USER_NUMBER)
            {
                log_i("Maximum user number reached, skipping additional users");
                break;
            }

            userInfo[userNumbers].id = patient_id;
            userInfo[userNumbers].type = String(patient_type);
            userInfo[userNumbers].name = String(patient_name);

            if (userInfo[userNumbers].url != NULL)
            {
                userInfo[userNumbers].url = String(patient_avatar);
            }
            else
            {
                userInfo[userNumbers].url = String("https://www.baidu.com");
            }

            if (userInfo[userNumbers].url.startsWith("https://"))
            {
                userInfo[userNumbers].url = "http://" + userInfo[userNumbers].url.substring(8);
            }
            log_i("conn_url = %s", userInfo[userNumbers].url.c_str());
            // user_img_path[userNumbers] = downloadImage(userNumbers, user_img_file_path.c_str());

            sprintf(data, "%s,%d,%s,%s;", patient_name, patient_id, patient_type, userInfo[userNumbers].url.c_str());
            accountDataWrite(data);
            log_i("id = %d, type = %s, name = %s, avatar = %s\n", patient_id, patient_type, patient_name, userInfo[userNumbers].url.c_str());
            // member_binding_scr_Family(account_cnt, patient_name, patient_id, patient_type);

            userNumbers++;
            account_cnt++;
        }
        if (userNumbers == 0)
        {
            log_i("display nobody family");
            member_noboday_scr_Family();
        }
        else
        {
            log_i("display family");
            display_family_member();
        }
        bg_awake();
    }

    // event command
    if (!String(topic).compareTo(SubTopicHeader[1]))
    {
        // 确保必要字段存在
        if (!jsonBuffer.containsKey("title") || !jsonBuffer.containsKey("body"))
        {
            log_i("Missing required fields in event command");
            return;
        }

        const char *title = jsonBuffer["title"];
        const char *body = jsonBuffer["body"];

        if (messageNumber < MAX_MESSAGE_NUMBER)
        {
            messageNumber++;
            messageEvent[messageNumber - 1].title = String(title);
            messageEvent[messageNumber - 1].body = String(body);
            messageEvent[messageNumber - 1].time = getFormatterDate().c_str();

            sprintf(data, "%s,%s,%s;", messageEvent[messageNumber - 1].title.c_str(), messageEvent[messageNumber - 1].body.c_str(), messageEvent[messageNumber - 1].time.c_str());
            log_i("messageNumber = %d", messageNumber);
            messageDataWrite(data);
            messageDisplayAll();
            msg_flag = 1;
        }
        else
        {
            log_w("arrived the max user index");
        }
        bg_awake();
    }

    // config command
    if (!String(topic).compareTo(SubTopicHeader[2]))
    {
        // 确保timezone字段存在
        if (!jsonBuffer.containsKey("timezone"))
        {
            log_i("Missing timezone field in config command");
            return;
        }
        TimeZone = jsonBuffer["timezone"];
        timeClient.setTimeOffset(TimeZone * 3600);
        bg_awake();
    }

    // ota command
    if (!String(topic).compareTo(SubTopicHeader[3]))
    {
        // 确保必要字段存在
        if (!jsonBuffer.containsKey("download_url") || !jsonBuffer.containsKey("version"))
        {
            log_i("Missing required fields in OTA command");
            return;
        }

        const char *update_url = jsonBuffer["download_url"];
        const char *ser_version = jsonBuffer["version"];
        otaUrl = String(update_url);
        ServerFwVersion = String(ser_version);
        versionUpdataToLabel();

        // if (otaUrl.startsWith("https://"))
        // {
        //     otaUrl = "http://" + otaUrl.substring(8);
        // }
        log_i("otaUrl:%s\nversion:%s\n", otaUrl.c_str(), ser_version);
        bg_awake();
    }

    // devices command
    if (!String(topic).compareTo(SubTopicHeader[4]))
    {
        // 确保payload是一个JSON数组
        if (!jsonBuffer.is<JsonArray>())
        {
            log_i("Expected JSON array for devices command, but got different format");
            return;
        }
        if (bleTrustNumber != 0)
        {
            bleInfoDataClean();
            bleTrustNumber = 0;
        }
        for (JsonObject item : jsonBuffer.as<JsonArray>())
        {
            // 检查必要字段是否存在
            if (!item.containsKey("mac_address") || !item.containsKey("device_type") ||
                !item.containsKey("upload_type") || !item.containsKey("model") ||
                !item.containsKey("manufacturer"))
            {
                log_i("Missing required fields in device item");
                continue;
            }

            // 检查数组边界
            if (bleTrustNumber >= MAX_BLE_TRUST_NUMBER)
            {
                log_i("Maximum BLE trust number reached, skipping additional devices");
                break;
            }

            const char *mac_address = item["mac_address"];
            uint16_t device_type = item["device_type"];
            uint16_t upload_type = item["upload_type"];
            const char *model = item["model"];
            const char *manufacturer = item["manufacturer"];

            bleTrustInfo[bleTrustNumber].addr = String(mac_address);
            bleTrustInfo[bleTrustNumber].type = device_type;
            bleTrustInfo[bleTrustNumber].upload_type = upload_type;
            bleTrustInfo[bleTrustNumber].model = String(model);
            bleTrustInfo[bleTrustNumber].manufacturer = String(manufacturer);

            sprintf(data, "%s,%d,%d,%s,%s;", mac_address, device_type, upload_type, model, manufacturer);
            bleInfoDataWrite(data);
            log_i("mac_address = %s,device_type = %d,upload_type = %d, model = %s, manufacturer = %s\n", mac_address, device_type, upload_type, model, manufacturer);

            bleTrustNumber++;
        }

        if (bleTrustNumber == 0)
        {
            info_modify_Vitals_hide_data();
        }
        else
        {
            info_modify_Vitals_disp_data();
        }

        bg_awake();
    }

    // binding state
    if (!String(topic).compareTo(SubTopicHeader[5]))
    {
        // 确保status字段存在
        if (!jsonBuffer.containsKey("status"))
        {
            log_i("Missing status field in binding state command");
            return;
        }

        binding_state = jsonBuffer["status"];
        if_rcv_binding_sta = binding_state;
        log_i("binding_state = %d\n", binding_state);
        // 绑定成功后直接切换凭证并重连，避免重启
        if (binding_state == 1)
        {
            if (device_first_binding_flag == 1)
            {
                log_i("Binding success, switching to device credentials");
                // 切换为设备凭证
                device_first_binding_flag = 0; // 绑定成功后设置为0
                // 断开当前连接
                mqttClientDisconnect();
                // 触发重新连接流程
                ServerState.store(SERVER_DISCONNECTED);
            }
        }
        bg_awake();
    }

    // received default topic message
    if (!String(topic).compareTo(DefaultSubTopic))
    {
        // 确保status字段存在
        if (!jsonBuffer.containsKey("status"))
        {
            log_i("Missing status field in default topic message");
            return;
        }

        boolean state = jsonBuffer["status"];
        log_i("default state = %d\n", state);

        // 断开连接
        // client.disconnect();
        mqtt_reconnect_flag = 1;
        // wifi_mqtt_server_set();
        bg_awake();
    }
}

void mqttClientDisconnect()
{
    if (client.connected())
    {
        client.disconnect();
    }
}

void onMqttDisconnect(bool sessionPresent)
{
    log_i("mqtt disconnect start");
    // device_first_binding_flag = 0;
    MqttState.store(MQTT_CLIENT_DISCONNECTED); // 重置MQTT状态
    if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
    {
        mqtt_connect_status = 0xFF; // 重置MQTT状态
        xSemaphoreGive(xMqttStatusMutex);
    }

    if (client.connected())
    {
        client.disconnect();
    }
    clientSecure.stop();
    clientSecure.flush();

    // 延迟一下让状态生效
    delay(100);
    // 重新触发连接流程
    ServerState.store(SERVER_CONNECTING);
    log_i("mqtt disconnect end");
}

void device_recovery(void)
{
    static uint8_t step = 0;
    if (step == 0)
    {
        // 向服务器发送解绑确认
        if (binding_state == 0)
        {
            debinding_reply();
        }
        else if (recovery_state == 0)
        {
            recovery_reply();
        }
        step = 1;
    }
    else if (step == 1)
    {
        if (ServerState.load() == SERVER_CONNECTED)
        {
            // client.disconnect();
            ServerState.store(SERVER_DISCONNECTED);
        }

        // 清除wifi数据
        wifiDataClean();

        // 清除消息数据
        messageDataClean();

        // 清除账户数据
        accountDataClean();

        // 清除信任列表数据
        bleInfoDataClean();

        // 清除睡眠时间数据
        sleepConfigClean();

        // 清除lastdata数据
        lastDataClean();

        // 清除UID
        cleanUid();

        ESP.restart();
    }
}

void device_binding_reply(void)
{
    if (binding_state == 0 || recovery_state == 0)
    {
        device_recovery();
    }
}

void display_family_member(void)
{
    if (get_cur_page()->body_obj == Family)
    {
        for (int i = 0; i < userNumbers; i++)
        {
            member_binding_scr_Family(i, userInfo[i].name.c_str(), userInfo[i].id, userInfo[i].type.c_str(), user_img_path[i].c_str());
        }
    }
}

void display_patient_name(uint8_t index)
{
    S_PatientInfo info;
    info.name = (char *)ps_malloc(userInfo[index].name.length() + 1);
    strcpy(info.name, userInfo[index].name.c_str());
    info_modify_Patient(info);
    free(info.name);
    info.name = nullptr;
}

void display_message(uint8_t index)
{
    S_MessageReadInfo info;
    info.title = (char *)ps_malloc(messageEvent[index].title.length() + 1);
    info.body = (char *)ps_malloc(messageEvent[index].body.length() + 1);
    strcpy(info.title, messageEvent[index].title.c_str());
    strcpy(info.body, messageEvent[index].body.c_str());
    info_modify_MessageRead(info);
    free(info.title);
    free(info.body);
}
// static uint32_t get_pcm_size(uint8_t *data)
// {
//     return (data[40] | data[41] << 8 | data[42] << 16 | data[43] << 24);
// }
// 当绑定完患者之后才会上传数据
// 发送的频道格式:clientId/$account_id/patientId/$patient_id/patientType/$patient_type
// 当接收到蓝牙设备完成测量的数据时才上传数据
boolean wifi_mqtt_pub_oxygen_data(S_BloodOxygen oxy_data)
{
    // 检查是否绑定了用户
    if (userNumbers == 0)
    {
        return false;
    }

    String publishServer;
    if (xSemaphoreTake(xUserInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

        publishServer = "clientId/" + String(str.c_str()) +
                        "/patientId/" + String(userInfo[currentUserIndex].id) +
                        "/patientType/" + String(userInfo[currentUserIndex].type);
        xSemaphoreGive(xUserInfoMutex);
    }

    if (ServerState.load() == SERVER_DISCONNECTED)
    {
        char data[100] = {0};
        sprintf(data,
                "oxygen:%d,%d,%d,%s;",
                oxy_data.SPO2,
                oxy_data.PulseRate,
                oxy_data.PIData,
                getUTCFormatterDate().c_str());
        log_i("%s\n", data);
        offlineDataWrite(data);
        return false;
    }
    StaticJsonDocument<256> doc;

    JsonObject doc_0 = doc.createNestedObject();
    doc_0["field_name"] = "heart_rate";
    doc_0["field_value"] = oxy_data.PulseRate;
    doc_0["measured_at"] = getUTCFormatterDate().c_str();

    JsonObject doc_1 = doc.createNestedObject();
    doc_1["field_name"] = "blood_oxygen";
    doc_1["field_value"] = oxy_data.SPO2;
    doc_1["measured_at"] = getUTCFormatterDate().c_str();

    char JsonRenameBuffer[256];
    serializeJson(doc, JsonRenameBuffer);
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, JsonRenameBuffer);

    if (pub_result)
    {
        // sprintf(data, "so2: %d%%, pr: %dbpm, pi: %.1f%%", oxy_data.SPO2, ble_disp_data.oxygen.PulseRate, ((float)ble_disp_data.oxygen.PIData) / 10);
        // pop_disp_message("oxygen", data, getFormatterTime().c_str());
        // if (audio_enable_flag == 1)
        // {
        //     // TODO 添加语音播放
        //     uint8_t tenbit, onebit, decbit;
        //     tenbit = 18 + getTen(oxy_data.PulseRate);
        //     onebit = getOne(oxy_data.PulseRate);
        // }
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:\n%s\n", pub_result, JsonRenameBuffer);
    return true;
}

boolean wifi_mqtt_pub_pressure_data(S_BloodPressure pres_data)
{
    // 检查是否绑定了用户
    if (userNumbers == 0)
    {
        return false;
    }
    String publishServer;
    if (xSemaphoreTake(xUserInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

        publishServer = "clientId/" + String(str.c_str()) +
                        "/patientId/" + String(userInfo[currentUserIndex].id) +
                        "/patientType/" + String(userInfo[currentUserIndex].type);
        xSemaphoreGive(xUserInfoMutex);
    }
    if (ServerState.load() == SERVER_DISCONNECTED)
    {
        char data[100] = {0};
        sprintf(data,
                "pressure:%d,%d,%d,%s;",
                pres_data.Systolic,
                pres_data.Diastolic,
                pres_data.Pulse,
                getUTCFormatterDate().c_str());
        log_i("%s\n", data);
        offlineDataWrite(data);
        return false;
    }
    StaticJsonDocument<384> doc;

    JsonObject doc_0 = doc.createNestedObject();
    doc_0["field_name"] = "high_blood_pressure";
    doc_0["field_value"] = pres_data.Systolic;
    doc_0["measured_at"] = getUTCFormatterDate().c_str();

    JsonObject doc_1 = doc.createNestedObject();
    doc_1["field_name"] = "low_blood_pressure";
    doc_1["field_value"] = pres_data.Diastolic;
    doc_1["measured_at"] = getUTCFormatterDate().c_str();

    JsonObject doc_2 = doc.createNestedObject();
    doc_2["field_name"] = "heart_rate";
    doc_2["field_value"] = pres_data.Pulse;
    doc_2["measured_at"] = getUTCFormatterDate().c_str();

    char JsonRenameBuffer[384];
    serializeJson(doc, JsonRenameBuffer);
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, JsonRenameBuffer);
    if (pub_result)
    {
        // sprintf(data, "sys: %dmmHg,dia: %dmmHg,pul: %dbpm", pres_data.Systolic, pres_data.Diastolic, pres_data.Pulse);
        // pop_disp_message("pressure", data, getFormatterTime().c_str());
        if (audio_enable_flag == 1)
        {
            // TODO 添加语音播放
        }
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:\n%s\n", pub_result, JsonRenameBuffer);
    return true;
}

boolean wifi_mqtt_pub_weight_data(S_BodyWeight weig_data)
{
    // 检查是否绑定了用户
    if (userNumbers == 0)
    {
        return false;
    }
    String publishServer;
    if (xSemaphoreTake(xUserInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

        publishServer = "clientId/" + String(str.c_str()) +
                        "/patientId/" + String(userInfo[currentUserIndex].id) +
                        "/patientType/" + String(userInfo[currentUserIndex].type);
        xSemaphoreGive(xUserInfoMutex);
    }

    if (ServerState.load() == SERVER_DISCONNECTED)
    {
        char data[100] = {0};
        sprintf(data,
                "weight:%d,%d,%s;",
                (weig_data.WeightH << 8) | weig_data.WeightL,
                ((weig_data.ImpedanceH << 16) | (weig_data.ImpedanceM << 8) | (weig_data.ImpedanceL)),
                getUTCFormatterDate().c_str());
        log_i("%s\n", data);
        offlineDataWrite(data);
        return false;
    }
    StaticJsonDocument<256> doc;

    JsonObject doc_0 = doc.createNestedObject();
    doc_0["field_name"] = "weight";
    doc_0["field_value"] = (float)(((weig_data.WeightH << 8) | weig_data.WeightL) / 100.0);
    doc_0["measured_at"] = getUTCFormatterDate().c_str();

    JsonObject doc_2 = doc.createNestedObject();
    doc_2["field_name"] = "body_fat_resistance";
    doc_2["field_value"] = ((weig_data.ImpedanceH << 16) | (weig_data.ImpedanceM << 8) | (weig_data.ImpedanceL));
    doc_2["measured_at"] = getUTCFormatterDate().c_str();

    char JsonRenameBuffer[256];
    serializeJson(doc, JsonRenameBuffer);
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, JsonRenameBuffer);
    if (pub_result)
    {
        // sprintf(data, "weight: %.1flbs", ((float)((weig_data.WeightH << 8) | weig_data.WeightL)) * 2.2046 / 100);
        // pop_disp_message("weight", data, getFormatterTime().c_str());

        if (audio_enable_flag == 1)
        {
            // TODO 添加语音播放
        }
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:\n%s\n", pub_result, JsonRenameBuffer);
    return true;
}

boolean wifi_mqtt_pub_temperature_data(S_TemperatureAdv temp_data)
{
    // 检查是否绑定了用户
    if (userNumbers == 0)
    {
        return false;
    }
    String publishServer;
    if (xSemaphoreTake(xUserInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

        publishServer = "clientId/" + String(str.c_str()) +
                        "/patientId/" + String(userInfo[currentUserIndex].id) +
                        "/patientType/" + String(userInfo[currentUserIndex].type);
        xSemaphoreGive(xUserInfoMutex);
    }

    if (ServerState.load() == SERVER_DISCONNECTED)
    {
        char data[100] = {0};
        sprintf(data,
                "temperature:%d,%s;",
                (temp_data.TempH << 8) | temp_data.TempL,
                getUTCFormatterDate().c_str());
        log_i("%s\n", data);
        offlineDataWrite(data);
        return false;
    }

    StaticJsonDocument<128> doc;

    JsonObject doc_0 = doc.createNestedObject();
    doc_0["field_name"] = "body_temperature";
    doc_0["field_value"] = round((((float)((temp_data.TempH << 8) | temp_data.TempL)) * 9 / 500 + 32) * 100) / 100.0; // 转换成华氏度
    doc_0["measured_at"] = getUTCFormatterDate().c_str();

    char JsonRenameBuffer[128];
    serializeJson(doc, JsonRenameBuffer);
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, JsonRenameBuffer);
    if (pub_result)
    {
        // sprintf(data, "temerature: %.1fC", ((float)((temp_data.TempH << 8) | temp_data.TempL)) / 100);
        // pop_disp_message("temerature", data, getFormatterTime().c_str());
        if (audio_enable_flag == 1)
        {
            // TODO 添加语音播放
        }
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:\n%s\n", pub_result, JsonRenameBuffer);
    return true;
}

boolean wifi_mqtt_pub_blood_glucose_data(S_BloodGlucoseAdv glucose_data)
{
    // 检查是否绑定了用户
    if (userNumbers == 0)
    {
        return false;
    }
    String publishServer;
    if (xSemaphoreTake(xUserInfoMutex, portMAX_DELAY) == pdTRUE)
    {
        std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

        publishServer = "clientId/" + String(str.c_str()) +
                        "/patientId/" + String(userInfo[currentUserIndex].id) +
                        "/patientType/" + String(userInfo[currentUserIndex].type);
        xSemaphoreGive(xUserInfoMutex);
    }

    if (ServerState.load() == SERVER_DISCONNECTED)
    {
        char data[100] = {0};
        sprintf(data,
                "blood_glucose:%d,%d,%s;",
                glucose_data.Glucose,
                glucose_data.mealState,
                getUTCFormatterDate().c_str());
        log_i("%s\n", data);
        offlineDataWrite(data);
        return false;
    }

    StaticJsonDocument<16> doc_1;
    doc_1["selected_timing_id"] = info_modify_GlucoseDisplay_GetmealState();
    char JsonRenameBuffer_0[32];
    serializeJson(doc_1, JsonRenameBuffer_0);

    StaticJsonDocument<192> doc;
    JsonObject doc_0 = doc.createNestedObject();
    doc_0["field_name"] = "blood_glucose";
    doc_0["field_value"] = glucose_data.Glucose; // 转换成华氏度
    doc_0["measured_at"] = getUTCFormatterDate().c_str();
    doc_0["meta"] = JsonRenameBuffer_0;

    char JsonRenameBuffer[192];
    serializeJson(doc, JsonRenameBuffer);
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, JsonRenameBuffer);
    if (pub_result)
    {
        if (audio_enable_flag == 1)
        {
            // TODO 添加语音播放
        }
    }
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:\n%s\n", pub_result, JsonRenameBuffer);
    return true;
}

boolean wifi_mqtt_pub_server_password(void)
{
    std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

    String publishServer = "devices/provision";

    StaticJsonDocument<96> doc_0;
    doc_0["username"] = str.c_str();
    doc_0["password"] = MqttPass.c_str();

    char JsonRenameBuffer[96];
    serializeJson(doc_0, JsonRenameBuffer);
    boolean pub_result = client.publish(publishServer.c_str(), 0, 0, JsonRenameBuffer);
    if (pub_result)
    {
        if (audio_enable_flag == 1)
        {
            // TODO 添加语音播放
        }
    }
    MqttState.store(MQTT_CLIENT_RECONNECT_NEEDED);
    log_i("publishTopic:\n%s\n", publishServer.c_str());
    log_i("publishState: %d\npublishData:\n%s\n", pub_result, JsonRenameBuffer);
    return true;
}

void wifi_mqtt_topic_create(void)
{
    log_i("mqtt_topic_init");
    std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));

    SubTopicHeader[0] += String(str.c_str());
    SubTopicHeader[1] += String(str.c_str());
    SubTopicHeader[2] += String(str.c_str());
    SubTopicHeader[3] += String(str.c_str());
    SubTopicHeader[4] += String(str.c_str());
    SubTopicHeader[5] += String(str.c_str());
    DefaultSubTopic += String(str.c_str());
    DefaultSubTopic += DefaultSub;

    log_i("%s\n", String(str.c_str()).c_str());
}

// mqtt topic 订阅
void wifi_mqtt_sub()
{
    client.subscribe(SubTopicHeader[0].c_str(), 0);
    client.subscribe(SubTopicHeader[1].c_str(), 0);
    client.subscribe(SubTopicHeader[2].c_str(), 0);
    client.subscribe(SubTopicHeader[3].c_str(), 0);
    client.subscribe(SubTopicHeader[4].c_str(), 0);
    client.subscribe(SubTopicHeader[5].c_str(), 0);
}

void wifi_default_mqtt_sub()
{
    log_i("%s\n", DefaultSubTopic.c_str());
    client.subscribe(DefaultSubTopic.c_str(), 0);
}

void wifi_mqtt_unsub()
{
    client.unsubscribe(SubTopicHeader[0].c_str());
    client.unsubscribe(SubTopicHeader[1].c_str());
    client.unsubscribe(SubTopicHeader[2].c_str());
    client.unsubscribe(SubTopicHeader[3].c_str());
    client.unsubscribe(SubTopicHeader[4].c_str());
    client.unsubscribe(SubTopicHeader[5].c_str());
}
// 多wifi连接非阻塞代码
// uint8_t best_wifi_index = 0;
// uint8_t multi_connect_flag = 0; // 0：开始wifi扫描； 1：获取最强信号的wifi；2：wifi连接

struct TempWifiInfo
{
    uint8_t index; // 对应 wifiInfo 数组的索引
    int32_t rssi;
};

std::vector<TempWifiInfo> tempWifiList;
uint8_t best_wifi_index = 0;
// E_WifiScanSta multi_connect_flag = WIFI_SCAN_DEFAULT;
unsigned long connectStartTime = 0;
const unsigned long WIFI_CONNECT_TIMEOUT = 30000; // 30秒连接超时
const int MAX_RECONNECT_ATTEMPTS = 3;
int reconnectAttempts = 0;

void processScanResults(int8_t scanResult)
{
    int bestNetworkDb = -150; // 初始化为一个非常差的信号强度
    log_i("[WIFI] scan done");

    if (scanResult == 0)
    {
        log_e("[WIFI] no networks found");
        return;
    }

    log_i("[WIFI] %d networks found", scanResult);
    tempWifiList.clear();
    best_wifi_index = 0;

    for (int8_t i = 0; i < scanResult; ++i)
    {
        String ssid_scan;
        int32_t rssi_scan;
        uint8_t sec_scan;
        uint8_t *BSSID_scan;
        int32_t chan_scan;

        WiFi.getNetworkInfo(i, ssid_scan, sec_scan, rssi_scan, BSSID_scan, chan_scan);

        for (uint8_t j = 0; j < getWifiStorageCount(); ++j)
        {
            if (getWifiInfo(j).ssid == ssid_scan)
            {
                tempWifiList.push_back({j, rssi_scan});
                if (rssi_scan > bestNetworkDb)
                {
                    bestNetworkDb = rssi_scan;
                    best_wifi_index = j;
                }
                break;
            }
        }
    }

    log_i("bestNetwork index = %d", best_wifi_index);
    WiFi.scanDelete();
}

bool scanWifiNetworks()
{
    int8_t scanResult = WiFi.scanComplete();
    if (scanResult == WIFI_SCAN_RUNNING)
    {
        return false;
    }
    if (scanResult >= 0)
    {
        processScanResults(scanResult);
        return true;
    }
    return false;
}

bool connectToWifi(const char *ssid, const char *password)
{
    WiFi.begin(ssid, password);
    log_i("Connecting to WiFi: %s", ssid);
    return true;
}

uint8_t wifi_multi_connect()
{
    uint8_t status = WiFi.status();
    if (status == WL_CONNECTED)
    {
        multi_connect_flag.store(WIFI_SCAN_DEFAULT);
        reconnectAttempts = 0;
        return status;
    }

    switch (multi_connect_flag.load())
    {
    case WIFI_SCAN_DEFAULT:
        multi_connect_flag.store(WIFI_SCAN_START);
        WiFi.scanNetworks(true);
        log_i("[WIFI] start scan");
        break;

    case WIFI_SCAN_START:
        if (scanWifiNetworks())
        {
            multi_connect_flag.store(WIFI_SCAN_START_CONNECT);
        }
        break;

    case WIFI_SCAN_START_CONNECT:
        if (tempWifiList.empty())
        {
            log_e("No known WiFi networks found");
            multi_connect_flag.store(WIFI_SCAN_DEFAULT);
            break;
        }
        multi_connect_flag.store(WIFI_SCAN_CONNECTING);
        // 使用互斥锁保护wifiInfo访问
        if (xSemaphoreTake(xWifiMutex, portMAX_DELAY) == pdTRUE)
        {
            if (!connectToWifi(getWifiInfo(best_wifi_index).ssid.c_str(), getWifiInfo(best_wifi_index).psw.c_str()))
            {
                log_e("Failed to initiate WiFi connection");
                multi_connect_flag.store(WIFI_SCAN_DEFAULT);
            }
            else
            {
                connectStartTime = millis();
            }
            xSemaphoreGive(xWifiMutex);
        }
        break;

    case WIFI_SCAN_CONNECTING:
        if (status == WL_CONNECTED)
        {
            log_i("Successfully connected to WiFi");
            multi_connect_flag.store(WIFI_SCAN_DEFAULT);
            reconnectAttempts = 0;
        }
        else if (millis() - connectStartTime > WIFI_CONNECT_TIMEOUT)
        {
            log_w("WiFi connection timeout");
            WiFi.disconnect();
            if (++reconnectAttempts < MAX_RECONNECT_ATTEMPTS)
            {
                log_i("Retrying connection, attempt %d", reconnectAttempts + 1);
                multi_connect_flag.store(WIFI_SCAN_START_CONNECT);
            }
            else
            {
                log_e("Max reconnection attempts reached");
                multi_connect_flag.store(WIFI_SCAN_DEFAULT);
                reconnectAttempts = 0;
            }
        }
        break;
    }

    return status;
}

// 10s检查一次wifi连接状态,如果没有连接,则将wifi状态设置为WIFI_CONNECTING
void wifi_reconnect_handler(void)
{
    // 在主界面或者wifi配置时禁止检查wifi连接状态
    if (get_cur_page()->page_level < 2 || WifiState.load() == WIFI_SMARTCONFIG || WifiState.load() == WIFI_RECONNECT_SMARTCONFIG)
        return;

    static uint32_t wifi_reconnect_tick = 0;
    static bool is_first_boot = true;

    if (wifi_reconnect_tick++ > 50)
    {
        wifi_reconnect_tick = 0;
        if (WiFi.status() != WL_CONNECTED)
        {
            // WifiState.store(WIFI_CONNECTING);

            if (is_first_boot)
            {
                // 首次启动时需要显示连接界面
                if (get_cur_page()->body_obj != WifiConnectingFull)
                {
                    page_transition("WifiConnectingFull");
                }
                is_first_boot = false; // 清除首次启动标志
            }

            // 设置重连状态
            WifiState.store(WIFI_CONNECTING);
            multi_connect_flag.store(WIFI_SCAN_DEFAULT);
            wifi_reconnect_count = 3;
        }
    }
}

void ssl_connect_task_func(void *parameter)
{
    log_i("SSL connection task started");

    // 使用互斥锁保护状态变量
    if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
    {
        ssl_connect_status = 0xFF; // 设置为连接中状态
        xSemaphoreGive(xMqttStatusMutex);
    }

    bool result = clientSecure.connect("stage.fondcircle.com", 8883);

    // 再次使用互斥锁保护状态变量
    if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
    {
        if (result)
        {
            log_i("SSL connection successful");
            ssl_connect_status = 1;
        }
        else
        {
            log_i("SSL connection failed");
            ssl_connect_status = 0;
        }

        ssl_connect_task = NULL;
        xSemaphoreGive(xMqttStatusMutex);
    }
    vTaskDelete(NULL);
}

void mqtt_connect_task_func(void *parameter)
{
    log_i("MQTT connection task started");

    wifi_setup_mqtt();

    std::string str(DeviceUid, sizeof(DeviceUid) / sizeof(DeviceUid[0]));
    String willTopic = "clientId/" + String(str.c_str()) + "/status";
    String willMessage = "{\"status\": \"offline\"}";
    client.setWill(willTopic.c_str(), 1, true, willMessage.c_str(), willMessage.length());

    client.connect();

    if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
    {
        mqtt_connect_status = 0XFF;
        xSemaphoreGive(xMqttStatusMutex);
    }

    uint8_t timeout_count = 0; // 超时计数器

    while (!client.connected() && timeout_count < 30) // 最多等待10秒
    {
        delay(1000);
        log_i("Waiting for MQTT connection...");
        timeout_count++;
    }
    if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
    {
        if (client.connected())
        {
            mqtt_connect_status = 1;
            log_i("MQTT connection successful");
        }
        else
        {
            mqtt_connect_status = 0;
            log_i("MQTT connection timeout");
        }
        mqtt_connect_task = NULL;
        xSemaphoreGive(xMqttStatusMutex);
    }
    vTaskDelete(NULL);
}

// wifi连接函数,放在loop()中1s运行一次
uint8_t server_first_connect_flag = 0;
const uint8_t max_retries = 5;
void wifi_running_handler(void)
{
    S_WifiConnectInfo info;
    S_WifiConnctingInfo info2;
    static E_WifiSta previousState = WIFI_SMARTCONFIG;

    static E_WifiSta lastWifiState = WIFI_DISCONNECTED;
    E_WifiSta currentWifiState = WifiState.load();

    // 记录状态变化
    if (lastWifiState != currentWifiState)
    {
        log_i("WiFi state change: %d -> %d", lastWifiState, currentWifiState);
        lastWifiState = currentWifiState;
    }

    switch (WifiState.load())
    {
    case WIFI_DISCONNECTED: // disconnected
        ServerState.store(SERVER_DISCONNECTED);
        MqttState.store(MQTT_CLIENT_DISCONNECTED);
        multi_connect_flag.store(WIFI_SCAN_DEFAULT);
        // 使用互斥锁保护BLE状态
        portENTER_CRITICAL(&wifiStateMux);
        bleStateFlag = BLE_SCANNING;
        portEXIT_CRITICAL(&wifiStateMux);
        if_rcv_binding_sta = -1;

        info.name = (char *)ps_malloc(strlen("none") + 1);
        strcpy(info.name, "none");
        info.wifi = 0;
        info_modify_WifiConnectLv1(info);
        info_modify_WifiConnect(info);
        free(info.name);
        info.name = nullptr;

        info_modify_WifiConnectingFull_info_reset();
        server_first_connect_flag = 0;

        break;
    case WIFI_CONNECTED: // connected
        if (WiFi.status() != WL_CONNECTED)
        {
            log_i("WiFi连接已断开,重置状态");
            WiFi.disconnect();
            bleStateFlag = BLE_SCANNING;
            WifiState.store(WIFI_CONNECTING);
            ServerState.store(SERVER_DISCONNECTED);
            MqttState.store(MQTT_CLIENT_DISCONNECTED);

            // 重置SSL和MQTT连接状态
            if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
            {
                ssl_connect_status = 0xFF;
                mqtt_connect_status = 0xFF;
                xSemaphoreGive(xMqttStatusMutex);
            }

            // 清理连接
            if (client.connected())
            {
                client.disconnect();
            }
            clientSecure.stop();
            clientSecure.flush();

            // 延迟一段时间后重新触发连接
            delay(100);
            multi_connect_flag.store(WIFI_SCAN_DEFAULT);
        }

        // 处理MQTT重连需求
        if (MqttState.load() == MQTT_CLIENT_RECONNECT_NEEDED)
        {
            log_i("MQTT reconnect needed, restart WiFi connection");

            if (client.connected())
            {
                client.disconnect();
            }
            clientSecure.stop();
            clientSecure.flush();

            // 给系统足够时间清理资源
            delay(200);

            // 重置WiFi连接
            WiFi.disconnect(true);
            delay(500); // 短暂延迟确保断开

            // 重置状态机
            WifiState.store(WIFI_DISCONNECTED);
            ServerState.store(SERVER_DISCONNECTED);
            MqttState.store(MQTT_CLIENT_DISCONNECTED);

            // 延迟后再触发重连
            delay(200);
            WiFi.begin();
            WifiState.store(WIFI_CONNECTING);

            break;
        }
        // static uint32_t mqtt_reconnect_cnt = 0;
        switch (ServerState.load())
        {
        case SERVER_DISCONNECTED:
            wifi_setup_server();
            // 连接服务器
            // clientSecure.connect("emqx.fondcircle.com", 443);
            // 使用互斥锁检查和创建SSL连接任务
            if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
            {
                if (ssl_connect_task == NULL)
                {
                    uint8_t temp = 0;
                    temp = xTaskCreatePinnedToCore(
                        ssl_connect_task_func, // 任务函数
                        "ssl_connect_task",    // 任务名称
                        8192,                  // 堆栈大小
                        NULL,                  // 参数
                        2,                     // 优先级
                        &ssl_connect_task,     // 任务句柄
                        0                      // 核心
                    );
                    log_i("ssl_connect_task create sta: %d", temp);
                }
                xSemaphoreGive(xMqttStatusMutex);
            }
            ServerState.store(SERVER_CONNECTING);
            break;
        case SERVER_CONNECTING:
            // 检查服务器连接状态
            // 如果未连接成功,等待
            // 如果连接成功,设置mqtt并连接
            if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
            {
                if (ssl_connect_status == 1)
                {
                    if (mqtt_connect_task == NULL)
                    {
                        uint8_t temp = 0;
                        temp = xTaskCreatePinnedToCore(
                            mqtt_connect_task_func, // 任务函数
                            "mqtt_connect_task",    // 任务名称
                            8192,                   // 堆栈大小
                            NULL,                   // 参数
                            2,                      // 优先级
                            &mqtt_connect_task,     // 任务句柄
                            0                       // 核心
                        );
                        log_i("mqtt_connect_task create sta: %d", temp);
                    }
                    ServerState.store(SERVER_CONNECTED);
                    MqttState.store(MQTT_CLIENT_DISCONNECTED);
                }
                else if (ssl_connect_status == 0)
                {
                    ServerState.store(SERVER_DISCONNECTED);
                }
                xSemaphoreGive(xMqttStatusMutex);
            }
            break;
        case SERVER_CONNECTED:
            // 等待mqtt连接成功
            if (xSemaphoreTake(xMqttStatusMutex, portMAX_DELAY) == pdTRUE)
            {
                if (mqtt_connect_status == 1)
                {
                    xSemaphoreGive(xMqttStatusMutex);
                    if (MqttState.load() == MQTT_CLIENT_DISCONNECTED)
                    {
                        if (device_first_binding_flag == 0)
                        {
                            ServerState.store(SERVER_CONNECTED);

                            wifi_mqtt_sub();
                            offline_data_send_flag = 1;

                            info.name = new char[WiFi.SSID().length() + 1];
                            strcpy(info.name, WiFi.SSID().c_str());
                            info.wifi = 1;
                            info_modify_WifiConnectLv1(info);
                            info_modify_WifiConnect(info);
                            delete[] info.name;
                            info.name = nullptr;
                            // ~界面显示
                            server_first_connect_flag = 1;

                            log_i("start mqtt sub\n");
                            MqttState.store(MQTT_CLIENT_CONNECTED);
                        }
                        else if (device_first_binding_flag == 1)
                        {
                            ServerState.store(SERVER_CONNECTED);
                            // timeClient.begin();
                            wifi_default_mqtt_sub();

                            info.name = new char[WiFi.SSID().length() + 1];
                            strcpy(info.name, WiFi.SSID().c_str());
                            info.wifi = 1;
                            info_modify_WifiConnectLv1(info);
                            info_modify_WifiConnect(info);
                            delete[] info.name;
                            info.name = nullptr;
                            // ~界面显示
                            server_first_connect_flag = 1;

                            device_first_binding_flag = 0;

                            delay(500);

                            wifi_mqtt_pub_server_password();

                            log_i("start default mqtt sub\n");
                            MqttState.store(MQTT_DEFAULT_CLIENT_CONNECTED);
                        }
                    }
                    else if (MqttState.load() == MQTT_DEFAULT_CLIENT_CONNECTED)
                    {
                        if (mqtt_reconnect_flag == 1)
                        {
                            mqtt_reconnect_flag = 0;
                            log_i("start disconncet default mqtt\n");
                            mqttClientDisconnect();
                            log_i("end disconncet default mqtt\n");
                        }
                    }
                }
                else if (mqtt_connect_status == 0)
                {
                    xSemaphoreGive(xMqttStatusMutex);
                    device_first_binding_flag = 1;
                    ServerState.store(SERVER_CONNECTING);
                }
                else
                {
                    xSemaphoreGive(xMqttStatusMutex);
                }
            }
            break;
        default:
            break;
        }

        // 收到用户的信息
        if (if_rcv_binding_sta == 1)
        {
            if (binding_recv_flag == 1 && server_first_connect_flag == 1)
            {
                binding_recv_flag = 0;
                server_first_connect_flag = 0;
                pop_off();

                if (get_cur_page()->page_level >= 3)
                    return;

                // 安全地管理页面栈：先删除旧栈，再创建新栈
                delete_page_stack();    // 删除旧栈
                re_create_page_stack(); // 立即创建新栈

                // 执行页面跳转
                if (userNumbers == 1)
                {
                    page_transition("Patient");
                    display_patient_name(0);
                    // 安全地添加页面到栈
                    page_push_no_display("Family");
                }
                else
                {
                    page_transition("Family");
                }
            }
        }
        // 移除设备重启逻辑，直接保持连接
        // 时间同步逻辑（增加错误处理和重试机制）
        static int8_t temp_time_count = 0;
        static uint8_t retry_count = 0;

        static bool first_sync = true;

        if (first_sync || temp_time_count-- <= 0)
        {
            if (timeClient.update())
            {
                // 同步成功
                retry_count = 0;
                temp_time_count = 60; // 正常60秒间隔
                first_sync = false;
                log_i("NTP sync successful: %s", getFormatterDate().c_str());
            }
            else
            {
                // 同步失败
                retry_count = (retry_count < max_retries) ? retry_count + 1 : max_retries;
                temp_time_count = (1 << retry_count); // 指数退避: 2,4,8,16,32秒
                log_e("NTP sync failed, retry in %d seconds (attempt %d/%d)",
                      temp_time_count, retry_count, max_retries);

                // 尝试切换NTP服务器作为备用方案
                if (retry_count > 2)
                {
                    timeClient.setPoolServerName("pool.ntp.org");
                    log_i("Switching to backup NTP server: pool.ntp.org");
                }
            }
        }
        break;
    case WIFI_CONNECTING: // connecting
        if (wifi_multi_connect() == WL_CONNECTED)
        {
            portENTER_CRITICAL(&wifiStateMux);
            bleStateFlag = BLE_SCANNING;
            portEXIT_CRITICAL(&wifiStateMux);
            WifiState.store(WIFI_CONNECTED);
            timeClient.begin();
            wifi_connect_tick = 0;
            info2.state_info = new char[strlen("Initializing device. Please wait.") + 1];
            strcpy(info2.state_info, "Initializing device. Please wait.");
            info_modify_WifiConnectingFull(info2);
            delete (info2.state_info);

            log_i("connect done\n");
        }
        else
        {
            ServerState.store(SERVER_DISCONNECTED);
            log_i("wifi connecting...\n");

            if (getBleStopFlag() == 1)
            {
                ble_adv_stop();
            }

            wifi_connect_tick++;
            if (wifi_connect_tick > 10)
            {
                wifi_connect_tick = 0;
                log_i("wifi disconnect\n");
                WiFi.disconnect();

                if (wifi_reconnect_count != 0)
                {
                    wifi_reconnect_count--;
                    wifi_connect_tick = 0;
                    multi_connect_flag.store(WIFI_SCAN_DEFAULT);
                }
                else
                {
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_SCANNING;
                    portEXIT_CRITICAL(&wifiStateMux);
                    WifiState.store(WIFI_DISCONNECTED);

                    // 如果当前页面是connectfull页面,则跳转到failedfull页面
                    if (get_cur_page()->body_obj == WifiConnectingFull)
                    {
                        // 显示连接失败页面
                        page_transition("WifiFailedFull");
                    }
                }
            }
        }
        break;
    case WIFI_RECONNECT_SMARTCONFIG: // 重新配网状态
        // 这里的处理与 WIFI_SMARTCONFIG 类似，但UI处理不同

        if (ServerState.load() != SERVER_DISCONNECTED)
        {
            ServerState.store(SERVER_DISCONNECTED);
        }

        if (client.connected())
        {
            client.disconnect();
        }

        if (clientSecure.connected())
        {
            clientSecure.stop();
            clientSecure.flush();
        }

        if (getWifiConfigFlag())
        {
            WiFi.begin(getWifiSSID(), getWifiPassword());
            WifiState.store(WIFI_RECONNECT_CONNECTED);
            previousState = WIFI_RECONNECT_SMARTCONFIG;
            timeClient.begin();

            // 使用不同的UI更新逻辑
            info2.state_info = new char[strlen("Reconnecting to new network...") + 1];
            strcpy(info2.state_info, "Reconnecting to new network...");
            info_modify_WifiConnectingFull(info2);
            delete[] info2.state_info;
            info2.state_info = nullptr;

            log_i("wifi reconnect config done\n");
            wifi_reconnect_count = 3;
        }
        else
        {
            ServerState.store(SERVER_DISCONNECTED);

            info.name = (char *)ps_malloc(strlen("smartConfig...") + 1);
            strcpy(info.name, "smartConfig...");
            info.wifi = 0;
            info_modify_WifiConnect(info);
            free(info.name); // 使用free而不是delete[]
            info.name = NULL;

            info_modify_WifiConnectingFull_info_reset();
        }
        break;
    case WIFI_SMARTCONFIG: // wifi config

        if (ServerState.load() != SERVER_DISCONNECTED)
        {
            ServerState.store(SERVER_DISCONNECTED);
        }

        if (client.connected())
        {
            client.disconnect();
        }

        if (clientSecure.connected())
        {
            clientSecure.stop();
            clientSecure.flush();
        }

        if (getWifiConfigFlag())
        {
            WiFi.begin(getWifiSSID(), getWifiPassword());
            WifiState.store(WIFI_FIRST_CONNECTED);
            previousState = WIFI_SMARTCONFIG;
            timeClient.begin();
            info2.state_info = new char[strlen("Initializing device. Please wait.") + 1];
            strcpy(info2.state_info, "Initializing device. Please wait.");
            info_modify_WifiConnectingFull(info2);
            delete (info2.state_info);
            log_i("wifi config done\n");
            wifi_reconnect_count = 3;
        }
        else
        {
            ServerState.store(SERVER_DISCONNECTED);

            // ~界面显示
            info_modify_WifiConnectingFull_info_reset();
        }
        break;
    case WIFI_RECONNECT_CONNECTED:
        if (WiFi.status() == WL_CONNECTED)
        {
            char data[50] = {0};
            int8_t duplicate_data_flag = -1; // 重复的WiFi
            static uint8_t first_connect_step = 0;

            if (first_connect_step == 0)
            {
                sprintf(data, "%s,%s;", WiFi.SSID().c_str(), WiFi.psk().c_str());
                log_i("connected :%s", WiFi.SSID().c_str());
                for (int i = 0; i < MAX_WIFI_NUMBER; i++)
                {
                    log_i("storaged :%s", getWifiInfo(i).ssid.c_str());
                    if (strcmp(getWifiInfo(i).ssid.c_str(), WiFi.SSID().c_str()) == 0)
                    {
                        duplicate_data_flag = i;
                        break;
                    }
                }
                // 检测到不是重复的wifi,则写入到flash
                if (duplicate_data_flag == -1)
                {
                    log_i("write wifi :%s", WiFi.SSID().c_str());
                    wifiDataWrite(data);
                }
                else
                {
                    log_i("wifi :%s is duplicate", WiFi.SSID().c_str());
                    wifiDataUpdate(data, duplicate_data_flag);
                }
                ble_wifi_config_success_notifiy();
                first_connect_step = 1;
            }
            else if (first_connect_step == 1)
            {
                // 添加延迟，确保BLE操作完成
                delay(100);
                first_connect_step = 2;
            }
            else if (first_connect_step == 2)
            {
                static uint8_t first_connect_delay_tick = 0;
                /* wifi数据接收成功，等待ble与主机断开连接*/
                if (getBleStopFlag() == 1 && bleStateFlag == BLE_BROADCAST)
                {
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_WIFI_CONNECT;
                    portEXIT_CRITICAL(&wifiStateMux);
                    ble_server_disconnect();
                }
                else if (getBleStopFlag() == 0 || ++first_connect_delay_tick > 5)
                {
                    first_connect_delay_tick = 0;
                    /*ble断开连接后停止广播后进行wifi的连接*/
                    first_connect_step = 0;
                    ble_adv_stop();
                    delay(100);
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_SCANNING;
                    portEXIT_CRITICAL(&wifiStateMux);

                    // 播放连接成功提示音
                    if (wav_exist_flag[NETWORK_SUCCESS])
                    {
                        audio_prompt(wav_file_path[NETWORK_SUCCESS].c_str());
                    }

                    // wifi_mqtt_server_set();

                    if (get_cur_page()->body_obj == Home)
                    {
                        page_transition("WifiConnectingFull");
                    }
                    else
                    {
                        pop_on("WifiComplete");
                    }

                    WifiState.store(WIFI_CONNECTED);
                    timeClient.begin();
                }
                log_i("first connect done\n");
            }
            wifi_connect_tick = 0;
        }
        else
        {
            ServerState.store(SERVER_DISCONNECTED);

            info.name = (char *)ps_malloc(strlen("connecting...") + 1);
            strcpy(info.name, "connecting...");
            info.wifi = 0;
            info_modify_WifiConnectLv1(info);
            info_modify_WifiConnect(info);
            free(info.name);
            info.name = nullptr;
            // ~界面显示

            wifi_connect_tick++;
            if (wifi_connect_tick > 10)
            {
                wifi_connect_tick = 0;
                log_i("wifi disconnect\n");
                WiFi.disconnect();
                ble_wifi_config_fail_notifiy();

                if (wifi_reconnect_count != 0)
                {
                    wifi_reconnect_count--;
                    multi_connect_flag.store(WIFI_SCAN_DEFAULT);
                }
                else
                {
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_BROADCAST;
                    portEXIT_CRITICAL(&wifiStateMux);
                    resetWifiConfigFlag();
                    WifiState.store(previousState); // 返回到配网阶段
                }
            }
        }
        break;
    case WIFI_FIRST_CONNECTED: // first connect
        if (WiFi.status() == WL_CONNECTED)
        {
            char data[50] = {0};
            static uint8_t first_connect_step = 0;

            if (first_connect_step == 0)
            {
                sprintf(data, "%s,%s;", WiFi.SSID().c_str(), WiFi.psk().c_str());
                log_i("connected :%s", WiFi.SSID().c_str());

                // 检测到不是重复的wifi,则写入到flash
                log_i("write wifi :%s", WiFi.SSID().c_str());
                wifiDataWrite(data);

                ble_wifi_config_success_notifiy();
                first_connect_step = 1;
            }
            else if (first_connect_step == 1)
            {
                // 添加延迟，确保BLE操作完成
                delay(100);
                first_connect_step = 2;
            }
            else if (first_connect_step == 2)
            {
                static uint8_t first_connect_delay_tick = 0;
                /* wifi数据接收成功，等待ble与主机断开连接*/
                if (getBleStopFlag() == 1 && bleStateFlag == BLE_BROADCAST)
                {
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_WIFI_CONNECT;
                    portEXIT_CRITICAL(&wifiStateMux);
                    ble_server_disconnect();
                }
                else if (getBleStopFlag() == 0 || ++first_connect_delay_tick > 5)
                {
                    first_connect_delay_tick = 0;
                    /*ble断开连接后停止广播后进行wifi的连接*/
                    first_connect_step = 0;
                    ble_adv_stop();
                    delay(100);
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_SCANNING;
                    portEXIT_CRITICAL(&wifiStateMux);

                    // 播放连接成功提示音
                    if (wav_exist_flag[NETWORK_SUCCESS])
                    {
                        audio_prompt(wav_file_path[NETWORK_SUCCESS].c_str());
                    }

                    // wifi_mqtt_server_set();

                    if (get_cur_page()->body_obj == Home)
                    {
                        page_transition("WifiConnectingFull");
                    }
                    else
                    {
                        pop_on("WifiComplete");
                    }

                    WifiState.store(WIFI_CONNECTED);
                    timeClient.begin();
                    device_first_binding_flag = 1;
                }
                log_i("first connect done\n");
            }
            wifi_connect_tick = 0;
        }
        else
        {
            ServerState.store(SERVER_DISCONNECTED);

            info.name = (char *)ps_malloc(strlen("connecting...") + 1);
            strcpy(info.name, "connecting...");
            info.wifi = 0;
            info_modify_WifiConnectLv1(info);
            info_modify_WifiConnect(info);
            free(info.name);
            info.name = nullptr;
            // ~界面显示

            wifi_connect_tick++;
            if (wifi_connect_tick > 10)
            {
                wifi_connect_tick = 0;
                log_i("wifi disconnect\n");
                WiFi.disconnect();
                ble_wifi_config_fail_notifiy();

                if (wifi_reconnect_count != 0)
                {
                    wifi_reconnect_count--;
                    multi_connect_flag.store(WIFI_SCAN_DEFAULT);
                }
                else
                {
                    portENTER_CRITICAL(&wifiStateMux);
                    bleStateFlag = BLE_BROADCAST;
                    portEXIT_CRITICAL(&wifiStateMux);
                    resetWifiConfigFlag();
                    WifiState.store(previousState); // 返回到配网阶段
                }
            }
        }
        break;
    default:
        break;
    }
}

// 配网按钮，这里并没有加入清除wifi配置的代码
void wifiConfigStart(void)
{
    E_WifiSta currentState = WifiState.load();
    if (currentState == WIFI_SMARTCONFIG || currentState == WIFI_RECONNECT_SMARTCONFIG)
    {
        return;
    }

    // 确定目标状态 - 区分初始配网和重新配网
    E_WifiSta targetState = (currentState == WIFI_CONNECTED) ? WIFI_RECONNECT_SMARTCONFIG : WIFI_SMARTCONFIG;

    log_i("Starting WiFi config, current state: %d, target state: %d", currentState, targetState);

    // 先停止蓝牙扫描
    bleStopScan();

    // 添加一个短暂延迟，确保扫描完全停止
    delay(50);

    // 确保先前的蓝牙操作已完全清理
    bleClearResults();

    if (client.connected())
    {
        log_i("Disconnecting MQTT client...");
        client.disconnect();
        delay(50);
    }

    if (clientSecure.connected())
    {
        log_i("Stopping SSL connection...");
        clientSecure.stop();
        clientSecure.flush();
        delay(100);
    }

    // 6. 断开WiFi连接
    if (WiFi.status() == WL_CONNECTED)
    {
        log_i("Disconnecting WiFi...");
        WiFi.disconnect(true, true);
        delay(200);
    }

    // 5. 重置服务器和MQTT状态
    ServerState.store(SERVER_DISCONNECTED);
    MqttState.store(MQTT_CLIENT_DISCONNECTED);

    // 最后更改WiFi状态
    WifiState.store(targetState);

    // 7. 确保之前的广播已停止
    if (getBleStopFlag() == 1)
    {
        log_i("Stopping BLE advertising...");
        ble_adv_stop();
        delay(50);
    }

    // 使用互斥锁保护状态变更
    portENTER_CRITICAL(&wifiStateMux);
    bleStateFlag = BLE_BROADCAST;
    portEXIT_CRITICAL(&wifiStateMux);

    // 启动广播
    ble_adv_start();
}

void connectWifi(void)
{
    if (WifiState.load() != WIFI_CONNECTED)
    {
        bleStopScan();
        if (getBleStopFlag() == 1)
        {
            ble_adv_stop();
        }
        bleStateFlag = BLE_WIFI_CONNECT;
        WiFi.mode(WIFI_STA);
        WifiState.store(WIFI_CONNECTING); // wait for wifi connected
        wifi_reconnect_count = 3;
    }
}

// wifi配网步骤：
// 1.打开蓝牙广播，等待主机连接
// 2.主机连接完毕后，等待发送wifi配置数据
// 3.接收到wifi配置数据，使用wifi配置数据进行连接
// 4.连接成功后保存当前的wifi配置
void wifi_server_default_set()
{
    // 设置mqtt服务器的地址
    // client.setServer(wss_server);
    client.setServer(MqttServer);
    client.attachArduinoCACertBundle();
    // client.setCACert(server_certificate, strlen(server_certificate));
    client.onMessage(messageReceived);
    client.onDisconnect(onMqttDisconnect);
    client.setBufferSize(2048);
    client.setKeepAlive(20);
}

void wifi_init()
{
    log_i("wifi_init");
    // 创建互斥锁
    xWifiMutex = xSemaphoreCreateMutex();
    xUserInfoMutex = xSemaphoreCreateMutex();
    xMqttStatusMutex = xSemaphoreCreateMutex();
    xWifiInfoMutex = xSemaphoreCreateMutex();

    // 设置WiFi连接的设备名称
    WiFi.mode(WIFI_STA); // 设置STA模式
    WiFi.setHostname(HostName);

    wifi_server_default_set();
}

void wifi_setup_server()
{
    // SSL配置前先清理
    clientSecure.stop();
    clientSecure.flush();

    // 创建安全客户端实例并配置证书
    clientSecure.setCACertBundle(rootca_crt_bundle_start);
    // clientSecure.setCACert(server_certificate);
}

void wifi_setup_mqtt()
{
    // 设置默认凭证
    if (device_first_binding_flag == 0)
    {
        client.setCredentials(MqttUser.c_str(), MqttPass.c_str());
        log_i("set mqtt credentials: %s, %s", MqttUser.c_str(), MqttPass.c_str());
    }
    else
    {
        client.setCredentials(DefaultMqttUser.c_str(), DefaultMqttPass.c_str());
        log_i("set mqtt credentials: %s, %s", DefaultMqttUser.c_str(), DefaultMqttPass.c_str());
    }
}

void wifi_data_read()
{
    log_i("wifi_data_read");
    // 读取内存中的wifi信息
    wifiDataRead();
    if (getWifiStorageCount() == 0)
    {
        // 跳转到home页面并开启广播
        log_i("no wifi data in flash\n");
        page_transition("Home");
    }
    else
    {
        log_i("start wifi connect\n");
        // 跳转到wifi连接界面
        // page_transition("WifiConnectLv1");
        // pop_on("WifiConnecting");
        page_transition("WifiConnectingFull");
        // 开始WiFi连接
        bleStateFlag = BLE_WIFI_CONNECT;
        // flash中有wifi信息，进入connecting状态
        WiFi.mode(WIFI_STA);
        // wait for wifi connected
        WifiState.store(WIFI_CONNECTING);

        wifi_reconnect_count = 3;
    }
}

void mqtt_handler()
{
    // 监测mqtt服务器的连接状态
    if (WifiState.load() == WIFI_CONNECTED)
    {
        // 10s检测一次，防止数据获取太频繁
        static uint8_t mqtt_connect_count = 0;
        if (mqtt_connect_count++ >= 10)
        {
            mqtt_connect_count = 0;
            if (!client.connected() && !otaFlag)
            {
                ServerState.store(SERVER_DISCONNECTED);

                // 检查SSL连接
                if (!clientSecure.connected())
                {
                    clientSecure.stop();
                }
            }
        }
    }
    else
    {
        ServerState.store(SERVER_DISCONNECTED);
    }
    // 检测网络连接状态
    if (ServerState.load() == SERVER_CONNECTED && otaFlag == 0)
    {
        if (WiFi.status() != WL_CONNECTED)
        {
            WiFi.disconnect();
            bleStateFlag = BLE_SCANNING;
            WifiState.store(WIFI_DISCONNECTED);
            ServerState.store(SERVER_DISCONNECTED);
        }
    }
}

/************************************ ble****************************************************/

char pop_disp_temp_data[100] = {0};
char pop_disp_temp_time[17] = {0};
uint8_t pop_disp_flag = 0;
// 血氧仪数据解析
S_BloodOxygen bloodOxygenData(uint8_t *payload)
{
    S_BloodOxygen data = {};
    static S_BloodOxygen lastData = {};
    static uint8_t send_flag = 1;

    if (payload[7] == 0x81)
    {
        data.PulseRate = payload[8];
        data.SPO2 = payload[9];
        data.PIData = payload[10];
        strcpy(data.time, getFormatterDate().c_str());

        if (data.PulseRate != 0xff && data.SPO2 != 0x7f && data.PIData != 0x00 &&
            (lastData.PulseRate != data.PulseRate ||
             lastData.SPO2 != data.SPO2 ||
             lastData.PIData != data.PIData))
        {
            send_flag = 0;

            lastData.PulseRate = data.PulseRate;
            lastData.SPO2 = data.SPO2;
            lastData.PIData = data.PIData;
            strcpy(lastData.time, data.time);

            S_OxygenInfo info;
            info.so2 = data.SPO2;
            info.pr = data.PulseRate;
            info.pi = data.PIData;
            strcpy(info.time, data.time);
            info_modify_Vitals_Oxygen(info);

            pop_disp_flag = DATA_TYPE_BLOOD_OXYGEN;
            blood_glucose_submit_flag = 0;
            memset(pop_disp_temp_data, 0x00, sizeof(pop_disp_temp_data));
            memset(pop_disp_temp_time, 0x00, sizeof(pop_disp_temp_time));
            sprintf(pop_disp_temp_data, "so2: %d%%\n pr: %dbpm\n pi: %.1f%%", data.SPO2, data.PulseRate, ((float)data.PIData) / 10);
            sprintf(pop_disp_temp_time, "%s", data.time);
            // info_modify_DataDisplay_Pop(pop_disp_temp_data, pop_disp_temp_time);
        }

        // 检测到手指离开后才上传最后一包数据
        if (data.PulseRate == 0xff && data.SPO2 == 0x7f && data.PIData == 0x00 && send_flag == 0)
        {
            send_flag = 1;
            data.PulseRate = lastData.PulseRate;
            data.SPO2 = lastData.SPO2;
            data.PIData = lastData.PIData;
            strcpy(data.time, lastData.time);
            memcpy(&ble_disp_data.oxygen, &data, sizeof(data));
            memcpy(&ble_store_data[currentUserIndex].oxygen, &data, sizeof(data));
            bool last_storge = wifi_mqtt_pub_oxygen_data(data);
            if (last_storge == true)
                lastDataWrite(currentUserIndex);
        }
    }
    return data;
}

// 血压仪数据解析
S_BloodPressure bloodPressureData(uint8_t *payload)
{
    S_BloodPressure data = {};
    static S_BloodPressure lastData = {};
    if (payload[10] == 0xCB)
    {
        data.PressH = payload[11];
        data.PressL = payload[12];
        // data.HeartBeat = payload[7+4];
        data.DetectMode = 0xCB;
        log_i("press = %d\n", data.PressH << 8 | data.PressL);
    }
    else if (payload[10] == 0xCC)
    {
        data.Systolic = payload[11];
        data.Diastolic = payload[12];
        data.Pulse = payload[13];
        data.DetectMode = 0xCC;
        strcpy(data.time, getFormatterDate().c_str());

        if (lastData.Systolic != data.Systolic ||
            lastData.Diastolic != data.Diastolic ||
            (lastData.Pulse != data.Pulse &&
             (data.Systolic != 0 && data.Diastolic != 0 && data.Pulse != 0)))
        {
            lastData.Systolic = data.Systolic;
            lastData.Diastolic = data.Diastolic;
            lastData.Pulse = data.Pulse;

            S_PressureInfo info;
            info.sys = data.Systolic;
            info.dia = data.Diastolic;
            info.pul = data.Pulse;
            strcpy(info.time, data.time);
            info_modify_Vitals_Pressure(info);

            memcpy(&ble_disp_data.pressure, &data, sizeof(data));
            memcpy(&ble_store_data[currentUserIndex].pressure, &data, sizeof(data));
            bool last_storge = wifi_mqtt_pub_pressure_data(data);
            if (last_storge == true)
                lastDataWrite(currentUserIndex);

            pop_disp_flag = DATA_TYPE_BLOOD_PRESSURE;
            blood_glucose_submit_flag = 0;
            memset(pop_disp_temp_data, 0x00, sizeof(pop_disp_temp_data));
            memset(pop_disp_temp_time, 0x00, sizeof(pop_disp_temp_time));
            sprintf(pop_disp_temp_data, "sys: %dmmHg\ndia: %dmmHg\npul: %dbpm", data.Systolic, data.Diastolic, data.Pulse);
            sprintf(pop_disp_temp_time, "%s", data.time);
            // info_modify_DataDisplay_Pop(pop_disp_temp_data, pop_disp_temp_time);
        }
    }
    return data;
}

// 体脂仪数据解析
S_BodyWeight bodyWeightData(uint8_t *payload)
{
    S_BodyWeight data = {};
    S_WeightInfo info = {};
    static uint8_t send_flag = 1;
    data.WeightL = payload[11];
    data.WeightH = payload[12];
    data.ImpedanceL = payload[13];
    data.ImpedanceM = payload[14];
    data.ImpedanceH = payload[15];
    data.Unit = (E_Unit)payload[16];
    strcpy(data.time, getFormatterDate().c_str());

    info.weight = (data.WeightH << 8) | data.WeightL;
    strcpy(info.time, data.time);

    if (info.weight != 0)
    {
        info_modify_Vitals_Weight(info);
    }

    if (payload[17] == 0)
    {
        if (send_flag == 0)
        {
            send_flag = 1;

            memcpy(&ble_disp_data.weight, &data, sizeof(data));
            memcpy(&ble_store_data[currentUserIndex].weight, &data, sizeof(data));
            bool last_storge = wifi_mqtt_pub_weight_data(data);
            if (last_storge == true)
                lastDataWrite(currentUserIndex);

            pop_disp_flag = DATA_TYPE_WEIGHT;
            blood_glucose_submit_flag = 0;
            memset(pop_disp_temp_data, 0x00, sizeof(pop_disp_temp_data));
            memset(pop_disp_temp_time, 0x00, sizeof(pop_disp_temp_time));
            sprintf(pop_disp_temp_data, "weight: %.1flbs", ((float)((data.WeightH << 8) | data.WeightL)) * 2.2046 / 100);
            sprintf(pop_disp_temp_time, "%s", data.time);
            // info_modify_DataDisplay_Pop(pop_disp_temp_data, data.tpop_disp_temp_timeime);
        }
    }
    else
    {
        send_flag = 0;
    }
    return data;
}

// 温度计数据解析
S_TemperatureAdv temperatureData(uint8_t *payload)
{
    S_TemperatureAdv data = {};
    static uint8_t lastCnt = 0xff;
    uint8_t Command = payload[9];
    data.DetectCnt = payload[10];
    data.TempH = payload[11];
    data.TempL = payload[12];
    strcpy(data.time, getFormatterDate().c_str());

    if (data.DetectCnt != 0 && Command == 0xc1)
    {
        S_TemperatureInfo info;
        info.temperature = (data.TempH << 8) | data.TempL;
        strcpy(info.time, data.time);
        info_modify_Vitals_Temperature(info);

        if (lastCnt != data.DetectCnt)
        {
            lastCnt = data.DetectCnt;
            memcpy(&ble_disp_data.temperature, &data, sizeof(data));
            memcpy(&ble_store_data[currentUserIndex].temperature, &data, sizeof(data));
            bool last_storge = wifi_mqtt_pub_temperature_data(data);
            if (last_storge == true)
                lastDataWrite(currentUserIndex);

            pop_disp_flag = DATA_TYPE_TEMPERATURE;
            blood_glucose_submit_flag = 0;
            memset(pop_disp_temp_data, 0x00, sizeof(pop_disp_temp_data));
            memset(pop_disp_temp_time, 0x00, sizeof(pop_disp_temp_time));
            sprintf(pop_disp_temp_data, "temerature: %.1fC", ((float)((data.TempH << 8) | data.TempL)) / 100);
            sprintf(pop_disp_temp_time, "%s", data.time);
        }
    }
    return data;
}

// void simulat_data(void)
// {
//     glu_data.DetectCnt = 1;
//     glu_data.Glucose = 100;
//     strcpy(glu_data.time, getFormatterDate().c_str());
//     pop_disp_flag = 1;
//     blood_glucose_submit_flag = 1;

//     memset(pop_disp_temp_data, 0x00, sizeof(pop_disp_temp_data));
//     memset(pop_disp_temp_time, 0x00, sizeof(pop_disp_temp_time));
//     sprintf(pop_disp_temp_data, "blood glucose: %dmm/dl", glu_data.Glucose);
//     sprintf(pop_disp_temp_time, "%s", glu_data.time);

//     log_i("simuat data");
// }

// 血糖数据解析
S_BloodGlucoseAdv bloodGlucoseData(uint8_t *payload)
{
    S_BloodGlucoseAdv data = {};
    static uint8_t lastCnt = 0xff;
    uint8_t Command = payload[9];

    if (Command == 0x02)
    {
        data.DetectCnt = payload[10];
        data.Glucose = payload[11];
        strcpy(data.time, getFormatterDate().c_str());

        S_BloodGlucoseInfo info;
        info.bloodGlucose = data.Glucose;
        strcpy(info.time, data.time);
        info_modify_Vitals_BloodGlucose(info);

        log_i("%d,%d", data.DetectCnt, data.Glucose);

        if (lastCnt != data.DetectCnt)
        {
            lastCnt = data.DetectCnt;
            // 弹窗的数据
            memcpy(&ble_disp_data.glucose, &data, sizeof(data));
            // 临时存储的数据,用作上传
            memcpy(&glu_data, &data, sizeof(data));
            // 保存在flash中的数据
            memcpy(&ble_store_data[currentUserIndex].glucose, &data, sizeof(data));

            lastDataWrite(currentUserIndex);

            // 血糖的弹窗需添加上传的按钮,暂时不添加,要进入到显示界面添加
            pop_disp_flag = DATA_TYPE_BLOOD_GLUCOSE;
            blood_glucose_submit_flag = 1;
            memset(pop_disp_temp_data, 0x00, sizeof(pop_disp_temp_data));
            memset(pop_disp_temp_time, 0x00, sizeof(pop_disp_temp_time));
            sprintf(pop_disp_temp_data, "blood glucose: %dmm/dl", data.Glucose);
            sprintf(pop_disp_temp_time, "%s", data.time);
        }
    }
    return data;
}

// 按下按钮后进行数据的上传及保存
bool blood_glucose_data_submit(void)
{
    bool last_storge = wifi_mqtt_pub_blood_glucose_data(glu_data);
    info_modify_Vitals_BloodGlucose_MealState(info_modify_GlucoseDisplay_GetmealState());
    if (last_storge == true)
    {
        memcpy(&ble_store_data[currentUserIndex].glucose, &glu_data, sizeof(glu_data));
        ble_store_data[currentUserIndex].glucose.mealState = info_modify_GlucoseDisplay_GetmealState();
        lastDataWrite(currentUserIndex);
    }
    return last_storge;
}

void bleScanRestart()
{
    if (pBLEScan != nullptr)
    {
        if (pBLEScan->isScanning() == false)
        {
            pBLEScan->clearResults();
            // Start scan with: duration = 0 seconds(forever), no scan end callback, not a continuation of a previous scan.
            pBLEScan->start(0, nullptr, false);
            esp_ble_tx_power_set(ESP_BLE_PWR_TYPE_SCAN, ESP_PWR_LVL_P21);
            log_i("restart the scan\n");
        }
    }
}

void bleStopScan()
{
    if (pBLEScan->isScanning() == true)
    {
        pBLEScan->stop();
        log_i("stop the ble scan\n");
    }
}

// 将搜索到的蓝牙设备的地址与信任列表中的地址进行比对，如果匹配成功，则将该设备的信任值设置为1，否则设置为0
S_TrustListCheck ble_check_device_address(NimBLEAddress address)
{
    S_TrustListCheck temp = {0, false};
    for (int i = 0; i < bleTrustNumber; i++)
    {
        uint8_t addressBytes[6];
        sscanf(bleTrustInfo[i].addr.c_str(), "%hhx:%hhx:%hhx:%hhx:%hhx:%hhx",
               &addressBytes[0], &addressBytes[1], &addressBytes[2],
               &addressBytes[3], &addressBytes[4], &addressBytes[5]);
        NimBLEAddress bleAddress(addressBytes);

        if (address.equals(bleAddress))
        {
            temp.cur_number = i;
            temp.inTrustList = true;
            return temp;
        }
    }
    return temp;
}

class MyAdvertisedDeviceCallbacks : public NimBLEAdvertisedDeviceCallbacks
{
    void onResult(NimBLEAdvertisedDevice *advertisedDevice)
    {
        // 首先检查RSSI值
        if (advertisedDevice->getRSSI() < RSSI_THRESHOLD)
        {
            // RSSI值太低，忽略这个设备
            return;
        }

        auto it = deviceHandlers.find(advertisedDevice->getName());
        if (it != deviceHandlers.end())
        {
            last_ble_msg_time = millis() / 1000;
            S_TrustListCheck check_result = ble_check_device_address(advertisedDevice->getAddress());
            if (advertisedDevice->haveManufacturerData() && check_result.inTrustList)
            {
                if (bleTrustInfo[check_result.cur_number].upload_type == 1)
                {
                    it->second(advertisedDevice, check_result);
                }
            }
        }
    }
};

// 处理血压设备的函数
void handleBloodPressureDevice(NimBLEAdvertisedDevice *advertisedDevice, const S_TrustListCheck &check_result)
{
    bleNoDataTimeCount = BLE_SCAN_TIME;
    std::string strManufacturerData = advertisedDevice->getManufacturerData();
    bloodPressureData((uint8_t *)strManufacturerData.c_str());
    bg_awake();
}

// 处理血氧设备的函数
void handleBloodOxygenDevice(NimBLEAdvertisedDevice *advertisedDevice, const S_TrustListCheck &check_result)
{
    bleNoDataTimeCount = BLE_SCAN_TIME;
    std::string strManufacturerData = advertisedDevice->getManufacturerData();
    bloodOxygenData((uint8_t *)strManufacturerData.c_str());
    bg_awake();
}

// 处理体重秤设备的函数
void handleWeighingScaleDevice(NimBLEAdvertisedDevice *advertisedDevice, const S_TrustListCheck &check_result)
{
    bleNoDataTimeCount = BLE_SCAN_TIME;
    std::string strManufacturerData = advertisedDevice->getManufacturerData();
    bodyWeightData((uint8_t *)strManufacturerData.c_str());
    bg_awake();
}

// 处理温度设备的函数
void handleTemperatureDevice(NimBLEAdvertisedDevice *advertisedDevice, const S_TrustListCheck &check_result)
{
    bleNoDataTimeCount = BLE_SCAN_TIME;
    std::string strManufacturerData = advertisedDevice->getManufacturerData();
    temperatureData((uint8_t *)strManufacturerData.c_str());
    bg_awake();
}

// 处理血糖设备的函数
void handleBloodGlucoseDevice(NimBLEAdvertisedDevice *advertisedDevice, const S_TrustListCheck &check_result)
{
    bleNoDataTimeCount = BLE_SCAN_TIME;
    std::string strManufacturerData = advertisedDevice->getManufacturerData();
    bloodGlucoseData((uint8_t *)strManufacturerData.c_str());
    bg_awake();
}

// 在setup函数或其他初始化函数中初始化deviceHandlers
void initializeDeviceHandlers()
{
    deviceHandlers[BloodPressureDevice_A] = &handleBloodPressureDevice;
    deviceHandlers[BloodPressureDevice_B] = &handleBloodPressureDevice;
    deviceHandlers[BloodOxygenDevice_A] = &handleBloodOxygenDevice;
    deviceHandlers[BloodOxygenDevice_B] = &handleBloodOxygenDevice;
    deviceHandlers[BloodOxygenDevice_C] = &handleBloodOxygenDevice;
    deviceHandlers[WeighingScaleDevice_A] = &handleWeighingScaleDevice;
    deviceHandlers[WeighingScaleDevice_B] = &handleWeighingScaleDevice;
    deviceHandlers[WeighingScaleDevice_C] = &handleWeighingScaleDevice;
    deviceHandlers[TemperatureDevice_A] = &handleTemperatureDevice;
    deviceHandlers[TemperatureDevice_B] = &handleTemperatureDevice;
    deviceHandlers[BloodGlucoseDevice_A] = &handleBloodGlucoseDevice;
    deviceHandlers[BloodGlucoseDevice_B] = &handleBloodGlucoseDevice;
}

void ble_init()
{
    log_i("ble_init");
    // 蓝牙服务器参数设置
    ble_server_init();
    // 添加特征值
    ble_add_server();
    // 蓝牙扫描参数设置
    ble_scan_init();
}

void ble_scan_init()
{
    BLEDevice::setScanFilterMode(CONFIG_BTDM_SCAN_DUPL_TYPE_DATA);
    BLEDevice::setScanDuplicateCacheSize(1000);
    pBLEScan = BLEDevice::getScan(); // create new scan
    pBLEScan->setAdvertisedDeviceCallbacks(new MyAdvertisedDeviceCallbacks(), true);
    pBLEScan->setActiveScan(true); // active scan uses more power, but get results faster
    pBLEScan->setInterval(100);
    pBLEScan->setWindow(99); // less or equal setInterval value
    pBLEScan->setMaxResults(0);

    initializeDeviceHandlers();
}

void ble_scan_handler()
{
    // 如果存在用户，则开启ble扫描,否则关闭扫描
    if (userNumbers != 0 && bleStateFlag == BLE_SCANNING)
    {
        if (--bleRestartScanTimeCount <= 0)
        {
            bleRestartScanTimeCount = 0;
            bleScanRestart();
        }
    }
    else
    {
        bleStopScan();
    }

    // 每隔5分钟停止蓝牙扫描3秒
    if (--bleNoDataTimeCount <= 0 && bleStateFlag == BLE_SCANNING)
    {
        bleRestartScanTimeCount = BLE_RESTART_TIME;
        bleNoDataTimeCount = BLE_SCAN_TIME;
        bleStopScan();
    }
}

/************************************ lvgl****************************************************/
void ui_log_impl(const char *file, int line, const char *format, ...)
{
    char buffer[256];
    const char *basename = strrchr(file, '\\');
    if (basename)
    {
        basename++; // 跳过反斜杠
    }
    else
    {
        // 尝试查找正斜杠，以防路径使用了正斜杠
        basename = strrchr(file, '/');
        if (basename)
        {
            basename++; // 跳过正斜杠
        }
        else
        {
            basename = file; // 如果没有找到任何斜杠，使用整个文件名
        }
    }
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    log_i("[%s:%d][UI] %s", basename, line, buffer);
}

void user_exit_press_event_handler(void)
{
    log_i("click exit button");

    if (system_state == false)
    {
        return;
    }

    // 安全检查当前页面
    if (get_cur_page() == NULL)
    {
        log_e("Exit button pressed but current page is NULL");
        return;
    }

    // 检查弹窗
    struct pop *current_pop = get_cur_pop();
    if (current_pop != NULL)
    {
        // 弹窗安全检查
        if (current_pop->name != NULL && strcmp(current_pop->name, "WifiConnecting") != 0)
        {
            pop_off();
        }
    }
    else
    {
        // 升级过程中不允许返回
        if (otaFlag != 1)
        {
            // 页面栈有效性检查
            if (get_cur_page()->page_level > 2)
            {
                return_prev_page();
            }
            else
            {
                log_i("Already at root level, cannot return");
            }
        }
    }
}

// void pop_disp_message(const char *topic, const char *body, const char *time)
// {
//     lv_label_set_text(popBoxtopicLabel, topic);
//     lv_label_set_text(popBoxbodyLabel, body);
//     lv_label_set_text(popBoxtimeLabel, time);
//     ui_pop_box_start(POP_ON);
//     messageDisplayTime = 3;
// }

void status_bar_disp_handler()
{
    if (info_modify_Vitals_get_display_flag() == 1 && get_cur_page()->body_obj == Vitals)
        return;
    // 时间
    S_StatusBarInfo info;
    strcpy(info.time, getFormatterTime().c_str());
    // 电量
    float temp_volage = get_power() * 3.3 / 4096 * 10.1;
    if (temp_volage > 3.7)
    {
        info.charge = 1;
    }
    else
    {
        info.charge = 2;
    }
    bool charge_state = digitalRead(POWER_CHARGE_IO) ? false : true;
    if (charge_state == true)
    {
        info.charge = 0;
    }

    // wifi
    info.wifi = WifiState.load() == WIFI_CONNECTED ? 1 : 0;
    info_modify_StatusBar(info);
}

void reset_pop_display_time(void)
{
    if (blood_glucose_submit_flag == 0)
    {
        pop_display_time = POP_DISPLAY_TIME;
    }
    else if (blood_glucose_submit_flag == 1)
    {
        pop_display_time = POP_DISPLAY_LONG_TIME;
    }
}

uint8_t pop_audio_flag = 0;
void data_popup_handler()
{
    // 添加空指针检查
    if (!get_cur_page())
    {
        log_e("Current page is null!");
        return;
    }

    // 更新弹窗数据显示
    if (pop_disp_flag != DATA_TYPE_NONE)
    {
        uint8_t if_refresh_pop_flag = 0;
        if_refresh_pop_flag = info_modify_DataDisplay_Pop(pop_disp_temp_data, pop_disp_temp_time);
        switch (pop_disp_flag)
        {
        case DATA_TYPE_BLOOD_GLUCOSE:
            pop_audio_flag = BLOOD_GLUCOSE_DATA;
            break;
        case DATA_TYPE_BLOOD_OXYGEN:
            if (if_refresh_pop_flag == 0)
                pop_audio_flag = BLOOD_OXYGEN_DATA;
            break;
        case DATA_TYPE_BLOOD_PRESSURE:
            pop_audio_flag = BLOOD_PRESSURE_DATA;
            break;
        case DATA_TYPE_WEIGHT:
            pop_audio_flag = WEIGHT_DATA;
            break;
        case DATA_TYPE_TEMPERATURE:
            pop_audio_flag = TEMPERATURE_DATA;
            break;
        default:
            break;
        }
        pop_disp_flag = DATA_TYPE_NONE;
    }

    // 退出弹窗
    if (get_cur_pop() != NULL)
    {
        log_i("cur_pop:%s", get_cur_pop()->name);
        if (!strncmp(get_cur_pop()->name, "DataDisplay", 11))
        {
            log_i("pop_display_time:%d", pop_display_time);
            if (--pop_display_time <= 0)
            {
                pop_off();
            }
        }
        else if (!strncmp(get_cur_pop()->name, "GlucoseDisplay", 14))
        {
            if (--pop_display_time <= 0)
            {
                blood_glucose_data_submit();
                pop_off();
            }
        }
    }
}

void my_disp_flush(lv_disp_drv_t *disp, const lv_area_t *area, lv_color_t *color_p)
{
    uint32_t w = (area->x2 - area->x1 + 1);
    uint32_t h = (area->y2 - area->y1 + 1);

    tft.startWrite();
    tft.setAddrWindow(area->x1, area->y1, w, h);
    tft.pushColors((uint16_t *)&color_p->full, w * h, true);
    tft.endWrite();

    lv_disp_flush_ready(disp);
}

void lvgl_init()
{
    log_i("lvgl_init");
    lv_init();

    tft.begin();        /* TFT init */
    tft.setRotation(3); /* Landscape orientation, flipped */

    // 分配buf1到内部RAM，buf2到外部PSRAM
    buf1 = (lv_color_t *)heap_caps_malloc(screenWidth * screenHeight * sizeof(lv_color_t), MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);
    buf2 = (lv_color_t *)heap_caps_malloc(screenWidth * screenHeight * sizeof(lv_color_t), MALLOC_CAP_SPIRAM | MALLOC_CAP_8BIT);

    if (buf1 == NULL || buf2 == NULL)
    {
        log_e("Failed to allocate display buffers");
        while (1)
            ; // 分配失败时挂起系统
    }

    lv_disp_draw_buf_init(&draw_buf, buf1, buf2, screenWidth * screenHeight);

    /*Initialize the display*/
    static lv_disp_drv_t disp_drv;
    lv_disp_drv_init(&disp_drv);
    /*Change the following line to your display resolution*/
    disp_drv.hor_res = screenWidth;
    disp_drv.ver_res = screenHeight;
    disp_drv.flush_cb = my_disp_flush;
    disp_drv.full_refresh = 1;
    disp_drv.draw_buf = &draw_buf;
    lv_disp_drv_register(&disp_drv);

    lv_port_indev_init();
    setup_ui();
    custom_init();
    lv_refr_now(lv_disp_get_default());
}

/************************************ storage****************************************************/
void createIfNotExists(const char *path, bool isDirectory)
{
    if (LittleFS.exists(path))
        return;

    bool success;
    if (isDirectory)
    {
        success = LittleFS.mkdir(path);
    }
    else
    {
        File f = LittleFS.open(path, FILE_WRITE);
        success = f;
        if (f)
            f.close();
    }

    log_i("%s %s: %s", success ? "Created" : "Failed to create",
          isDirectory ? "directory" : "file", path);
}
// 创建工程需要的文件，如果已经创建过就不创建
void dirAndFileCreate(void)
{
    const char *directories[] = {
        "/data", "/data/last", "/data/img", "/config", "/audio"};

    const char *files[] = {
        wifi_path.c_str(), account_path.c_str(), ble_info_path.c_str(),
        audio_config_path.c_str(), message_path.c_str(), offline_data_path.c_str(),
        sleep_config_path.c_str()};

    for (const auto &dir : directories)
    {
        createIfNotExists(dir, true);
    }

    for (const auto &file : files)
    {
        createIfNotExists(file, false);
    }

    for (int i = 0; i < MAX_USER_NUMBER; i++)
    {
        char user_file[30];
        snprintf(user_file, sizeof(user_file), "/data/last/patient_%d.txt", i);
        createIfNotExists(user_file, false);
    }
}

/*
1.wifi数据写入/读取函数
  "ssid,psw;..."
2.message数据写入/读取函数
  "titlt,body,time;..."
3.account数据写入/读取函数
  "name,id,type;..."
4.last数据写入/读取函数
  ""name":"data","value":"data","time":"data";..."
5.offline数据写入/读取函数
  ""name":"data","value":"data","time":"data";..."
6.删除文件中的字符串
*/
// wifi数据写入
bool wifiDataWrite(const char *data)
{
    File file = LittleFS.open(wifi_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    if (file.print(data))
    {
        log_d("- wifi data appended\n");
    }
    else
    {
        log_d("- append failed\n");
    }

    setWifiInfo(getWifiStorageCount(), WiFi.SSID(), WiFi.psk());
    incrementWifiStorageCount();
    file.close();
    return true;
}
bool wifiDataUpdate(const char *data, int index)
{
    // 首先,我们需要读取所有的WiFi信息
    File file = LittleFS.open(wifi_path, FILE_READ);
    if (!file)
    {
        log_i("- failed to open file for reading\n");
        return false;
    }

    String fileContent = file.readString();
    file.close();

    // 分割WiFi信息
    std::vector<String> wifiEntries;
    int start = 0;
    int end = fileContent.indexOf(';');
    while (end != -1)
    {
        wifiEntries.push_back(fileContent.substring(start, end + 1));
        start = end + 1;
        end = fileContent.indexOf(';', start);
    }

    // 更新指定索引的WiFi信息
    if (index < wifiEntries.size())
    {
        wifiEntries[index] = String(data);
    }

    // 重新写入所有WiFi信息
    file = LittleFS.open(wifi_path, FILE_WRITE);
    if (!file)
    {
        log_i("- failed to open file for writing\n");
        return false;
    }

    for (const auto &entry : wifiEntries)
    {
        file.print(entry);
    }

    file.close();
    // 更新wifiInfo数组
    setWifiInfo(index, WiFi.SSID(), WiFi.psk());

    log_d("- wifi data updated\n");
    return true;
}

bool wifiDataClean()
{
    if (LittleFS.remove(wifi_path))
    {
        // 重新创建空文件保持目录结构
        File file = LittleFS.open(wifi_path, FILE_WRITE);
        if (file)
            file.close();
        setWifiStorageCount(0);
        log_d("- wifi data cleared\n");
        return true;
    }
    log_i("- failed to remove wifi file\n");
    return false;
}

// wifi数据读取
bool wifiDataRead()
{
    File file = LittleFS.open(wifi_path);
    if (!file || file.isDirectory())
    {
        log_i("- failed to open file for reading\n");
        return false;
    }
    setWifiStorageCount(0);
    while (file.available())
    {
        String ssid = file.readStringUntil(',');
        String psw = file.readStringUntil(';');
        // 将读取的wifi信息存储到数组中
        setWifiInfo(getWifiStorageCount(), ssid, psw);
        incrementWifiStorageCount();
        log_i("%s,%s,%d\n", ssid.c_str(), psw.c_str(), getWifiStorageCount());
    }
    file.close();
    return true;
}

// message数据写入
bool messageDataWrite(const char *data)
{
    File file = LittleFS.open(message_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    if (file.print(data))
    {
        log_d("- message appended\n");
    }
    else
    {
        log_d("- append failed\n");
    }
    message_len[messageNumber] = file.size();
    file.close();
    return true;
}

bool messageDataClean()
{
    if (LittleFS.remove(message_path))
    {
        // 重新创建空文件保持目录结构
        File file = LittleFS.open(message_path, FILE_WRITE);
        if (file)
            file.close();
        for (int i = 0; i < MAX_MESSAGE_NUMBER; i++)
        {
            message_len[i] = 0;
        }
        log_d("- message data cleared\n");
        return true;
    }
    log_i("- failed to remove message file\n");
    return false;
}

// message数据读取
bool messageDataRead()
{
    File file = LittleFS.open(message_path);
    if (!file || file.isDirectory())
    {
        log_i("- failed to open file for reading\n");
        return false;
    }
    messageNumber = 0;
    messageFileLength = 0;
    while (file.available())
    {
        String topic = file.readStringUntil(',');
        String body = file.readStringUntil(',');
        String time = file.readStringUntil(';');
        messageEvent[messageNumber].title = topic;
        messageEvent[messageNumber].body = body;
        messageEvent[messageNumber].time = time;
        message_len[messageNumber] = messageFileLength;
        messageNumber++;
        messageFileLength += topic.length() + body.length() + time.length();
        if (messageNumber >= MAX_MESSAGE_NUMBER)
        {
            break;
        }
        log_i("%s,%s,%s;\n", topic.c_str(), body.c_str(), time.c_str());
    }
    file.close();
    return true;
}

// account数据写入
bool accountDataWrite(const char *data)
{
    File file = LittleFS.open(account_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    if (file.print(data))
    {
        log_d("- account appended\n");
    }
    else
    {
        log_d("- append failed\n");
    }
    file.close();
    return true;
}

bool accountDataClean()
{
    if (LittleFS.remove(account_path))
    {
        // 重新创建空文件保持目录结构
        File file = LittleFS.open(account_path, FILE_WRITE);
        if (file)
            file.close();
        log_d("- account data cleared\n");
        return true;
    }
    log_i("- failed to remove account file\n");
    return false;
}

// account数据读取
bool accountDataRead()
{
    File file = LittleFS.open(account_path);
    if (!file || file.isDirectory())
    {
        log_i("- failed to open file for reading\n");
        return false;
    }
    account_cnt = 0;
    while (file.available())
    {
        String name = file.readStringUntil(',');
        String id = file.readStringUntil(',');
        String type = file.readStringUntil(',');
        String url = file.readStringUntil(';');
        userInfo[account_cnt].name = name;
        userInfo[account_cnt].id = id.toInt();
        userInfo[account_cnt].type = type;
        userInfo[account_cnt].url = url;
        account_cnt++;
        if (account_cnt >= MAX_USER_NUMBER)
        {
            break;
        }
    }
    file.close();
    return true;
}

// bleInfo数据写入
bool bleInfoDataWrite(const char *data)
{
    File file = LittleFS.open(ble_info_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    if (file.print(data))
    {
        log_d("- bleInfo appended\n");
    }
    else
    {
        log_d("- append failed\n");
    }
    file.close();
    return true;
}

bool bleInfoDataClean()
{
    if (LittleFS.remove(ble_info_path))
    {
        // 重新创建空文件保持目录结构
        File file = LittleFS.open(ble_info_path, FILE_WRITE);
        if (file)
            file.close();
        log_d("- ble trust info cleared\n");
        return true;
    }
    log_i("- failed to remove ble info file\n");
    return false;
}

// bleInfo数据读取
bool bleInfoDataRead()
{
    File file = LittleFS.open(ble_info_path);
    if (!file || file.isDirectory())
    {
        log_i("- failed to open file for reading\n");
        return false;
    }
    bleTrustNumber = 0;
    while (file.available())
    {
        String addr = file.readStringUntil(',');
        String type = file.readStringUntil(',');
        String upload_type = file.readStringUntil(',');
        String model = file.readStringUntil(',');
        String manufacturer = file.readStringUntil(';');
        bleTrustInfo[bleTrustNumber].addr = addr;
        bleTrustInfo[bleTrustNumber].type = type.toInt();
        bleTrustInfo[bleTrustNumber].upload_type = upload_type.toInt();
        bleTrustInfo[bleTrustNumber].model = model;
        bleTrustInfo[bleTrustNumber].manufacturer = manufacturer;
        bleTrustNumber++;
        if (bleTrustNumber >= MAX_BLE_TRUST_NUMBER)
        {
            break;
        }
    }

    if (bleTrustNumber == 0)
    {
        info_modify_Vitals_hide_data();
    }
    else
    {
        info_modify_Vitals_disp_data();
    }

    file.close();
    return true;
}

// sleepConfig数据写入
bool sleepConfigWrite(const char *data)
{
    File file = LittleFS.open(sleep_config_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    log_i("info: %s", data);

    if (file.print(data))
    {
        log_d("- sleepConfig appended\n");
    }
    else
    {
        log_d("- append failed\n");
    }
    file.close();
    return true;
}

bool sleepConfigClean()
{
    if (LittleFS.remove(sleep_config_path))
    {
        // 重新创建空文件保持目录结构
        File file = LittleFS.open(sleep_config_path, FILE_WRITE);
        if (file)
            file.close();
        log_d("- sleep config cleared\n");
        return true;
    }
    log_i("- failed to remove sleep config file\n");
    return false;
}

// sleepConfig数据读取
bool sleepConfigRead()
{
    File file = LittleFS.open(sleep_config_path);
    if (!file || file.isDirectory())
    {
        log_i("- failed to open file for reading\n");
        return false;
    }

    while (file.available())
    {
        String sleepState = file.readStringUntil(';');
        S_SleepScreenInfo info;
        info.select = sleepState.toInt();
        info_modify_SleepScreen_Select(info);
        log_i("info.select = %d", info.select);
    }
    file.close();
    return true;
}

// audioConfig数据写入
bool audioConfigWrite(const char *data)
{
    File file = LittleFS.open(audio_config_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    if (file.print(data))
    {
        log_d("- audioConfig appended\n");
    }
    else
    {
        log_d("- append failed\n");
    }
    file.close();
    return true;
}

bool audioConfigClean()
{
    if (LittleFS.remove(audio_config_path))
    {
        // 重新创建空文件保持目录结构
        File file = LittleFS.open(audio_config_path, FILE_WRITE);
        if (file)
            file.close();
        log_d("- audio config cleared\n");
        return true;
    }
    log_i("- failed to remove audio config file\n");
    return false;
}

// audioConfig数据读取
bool audioConfigRead()
{
    File file = LittleFS.open(audio_config_path);
    if (!file || file.isDirectory())
    {
        log_i("- failed to open file for reading\n");
        return false;
    }

    while (file.available())
    {
        String audioState = file.readStringUntil(';');
        S_VoiceInfo info;
        info.state = audioState.toInt();
        info_modify_Voice_State(info);
    }
    file.close();
    return true;
}

// last数据写入 //数据较少，一次性写入
bool lastDataWrite(uint8_t cur_index)
{
    File file = LittleFS.open(last_data_path[cur_index], FILE_WRITE);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    char data[400] = {};
    sprintf(data,
            "%d,%d,%d,%s;%d,%d,%d,%s;%d,%d,%s;%d,%s;%d,%d,%s;",
            ble_store_data[cur_index].oxygen.SPO2,
            ble_store_data[cur_index].oxygen.PIData,
            ble_store_data[cur_index].oxygen.PulseRate,
            ble_store_data[cur_index].oxygen.time,

            ble_store_data[cur_index].pressure.Systolic,
            ble_store_data[cur_index].pressure.Diastolic,
            ble_store_data[cur_index].pressure.Pulse,
            ble_store_data[cur_index].pressure.time,

            (ble_store_data[cur_index].weight.WeightH << 8) | ble_store_data[cur_index].weight.WeightL,
            ((ble_store_data[cur_index].weight.ImpedanceH << 16) | (ble_store_data[cur_index].weight.ImpedanceM << 8) | (ble_store_data[cur_index].weight.ImpedanceL)),
            ble_store_data[cur_index].weight.time,

            (ble_store_data[cur_index].temperature.TempH << 8) | ble_store_data[cur_index].temperature.TempL,
            ble_store_data[cur_index].temperature.time,

            ble_store_data[cur_index].glucose.Glucose,
            ble_store_data[cur_index].glucose.mealState,
            ble_store_data[cur_index].glucose.time);
    if (file.print(data))
    {
        log_d("- last data write\n");
    }
    else
    {
        log_d("- write failed\n");
    }
    file.close();
    return true;
}

// last数据读取
bool lastDataRead()
{
    bool read_state = true;
    for (int i = 0; i < MAX_USER_NUMBER; i++)
    {
        File file = LittleFS.open(last_data_path[i]);
        if (!file || file.isDirectory())
        {
            log_i("- failed to open file for reading\n");
            read_state = false;
            continue;
        }
        if (!file.available())
        {
            log_i("- file%d is empty\n", i);
            file.close();
            read_state = false;
            continue;
        }

        // 血氧 so2,pi,pr,time;
        String _so2 = file.readStringUntil(',');
        String _pi = file.readStringUntil(',');
        String _pr = file.readStringUntil(',');
        String _time = file.readStringUntil(';');
        ble_disp_data.oxygen.SPO2 = (uint8_t)_so2.toInt();
        ble_disp_data.oxygen.PIData = (uint8_t)_pi.toInt();
        ble_disp_data.oxygen.PulseRate = (uint8_t)_pr.toInt();
        strcpy(ble_disp_data.oxygen.time, _time.c_str());
        memcpy(&ble_store_data[i].oxygen, &ble_disp_data.oxygen, sizeof(ble_disp_data.oxygen));
        // 血压 sys,dia,pul,time;
        String _sys = file.readStringUntil(',');
        String _dia = file.readStringUntil(',');
        String _pul = file.readStringUntil(',');
        _time = file.readStringUntil(';');
        ble_disp_data.pressure.Systolic = (uint8_t)_sys.toInt();
        ble_disp_data.pressure.Diastolic = (uint8_t)_dia.toInt();
        ble_disp_data.pressure.Pulse = (uint8_t)_pul.toInt();
        strcpy(ble_disp_data.pressure.time, _time.c_str());
        memcpy(&ble_store_data[i].pressure, &ble_disp_data.pressure, sizeof(ble_disp_data.pressure));
        // 体重 weight,impedance,time;
        String _weight = file.readStringUntil(',');
        String _impedance = file.readStringUntil(',');
        _time = file.readStringUntil(';');
        ble_disp_data.weight.WeightH = (uint8_t)(_weight.toInt() >> 8 & 0xff);
        ble_disp_data.weight.WeightL = (uint8_t)(_weight.toInt() >> 0 & 0xff);
        ble_disp_data.weight.ImpedanceH = (uint8_t)(_impedance.toInt() >> 16 & 0xff);
        ble_disp_data.weight.ImpedanceM = (uint8_t)(_impedance.toInt() >> 8 & 0xff);
        ble_disp_data.weight.ImpedanceL = (uint8_t)(_impedance.toInt() >> 0 & 0xff);
        strcpy(ble_disp_data.weight.time, _time.c_str());
        memcpy(&ble_store_data[i].weight, &ble_disp_data.weight, sizeof(ble_disp_data.weight));

        // 温度 temperature,time;
        String _temperature = file.readStringUntil(',');
        _time = file.readStringUntil(';');
        ble_disp_data.temperature.TempH = (uint8_t)(_temperature.toInt() >> 8 & 0xff);
        ble_disp_data.temperature.TempL = (uint8_t)(_temperature.toInt() >> 0 & 0xff);
        strcpy(ble_disp_data.temperature.time, _time.c_str());
        memcpy(&ble_store_data[i].temperature, &ble_disp_data.temperature, sizeof(ble_disp_data.temperature));

        // 血糖 blood_glucose,time;
        String _glucose = file.readStringUntil(',');
        String _mealState = file.readStringUntil(',');
        _time = file.readStringUntil(';');
        ble_disp_data.glucose.Glucose = _glucose.toInt();
        ble_disp_data.glucose.mealState = _mealState.toInt();
        strcpy(ble_disp_data.glucose.time, _time.c_str());
        memcpy(&ble_store_data[i].glucose, &ble_disp_data.glucose, sizeof(ble_disp_data.glucose));

        file.close();
        read_state = true;
    }
    return read_state;
}

bool lastDataClean(void)
{
    bool success = true;
    for (int i = 0; i < MAX_USER_NUMBER; i++)
    {
        if (LittleFS.exists(last_data_path[i]))
        {
            if (!LittleFS.remove(last_data_path[i]))
            {
                log_i("- failed to remove last data file: %s\n", last_data_path[i].c_str());
                success = false;
            }
            else
            {
                // 重新创建空文件保持目录结构
                File file = LittleFS.open(last_data_path[i], FILE_WRITE);
                if (file)
                    file.close();
            }
        }
    }
    return success;
}

// offline数据写入
bool offlineDataWrite(const char *data)
{
    File file = LittleFS.open(offline_data_path, FILE_APPEND);
    if (!file)
    {
        log_i("- failed to open file for appending\n");
        return false;
    }

    if (file.print(data))
    {
        log_d("- offline data write\n");
    }
    else
    {
        log_d("- write failed\n");
    }
    file.close();
    return true;
}

// offline数据读取 在网络恢复后再调用这个函数 type:xx,xx,xx..;
File offline_file;
void offlineDataRead()
{
    static uint8_t temp_flag = 0;
    static uint16_t str_length = 0;
    if (temp_flag == 0)
    {
        offline_file = LittleFS.open(offline_data_path);
        if (!offline_file || offline_file.isDirectory())
        {
            offline_data_send_flag = 0;
            log_i("- failed to open file for reading\n");
            return;
        }
    }
    if (offline_file.available())
    {
        String _type = offline_file.readStringUntil(':');
        if (!_type.compareTo("oxygen"))
        {
            // 血氧 so2,pi,pr,time;
            String _so2 = offline_file.readStringUntil(',');
            String _pi = offline_file.readStringUntil(',');
            String _pr = offline_file.readStringUntil(',');
            String _time = offline_file.readStringUntil(';');
            ble_disp_data.oxygen.SPO2 = (uint8_t)_so2.toInt();
            ble_disp_data.oxygen.PIData = (uint8_t)_pi.toInt();
            ble_disp_data.oxygen.PulseRate = (uint8_t)_pr.toInt();
            strcpy(ble_disp_data.oxygen.time, _time.c_str());

            String temp_str = "oxygen:" + _so2 + "," + _pi + "," + _pr + "," + _time + ";";
            str_length = strlen(temp_str.c_str());
            deleteString(offline_data_path.c_str(), 0, str_length);
            wifi_mqtt_pub_oxygen_data(ble_disp_data.oxygen);
        }
        else if (!_type.compareTo("pressure"))
        {
            // 血压 sys,dia,pul,time;
            String _sys = offline_file.readStringUntil(',');
            String _dia = offline_file.readStringUntil(',');
            String _pul = offline_file.readStringUntil(',');
            String _time = offline_file.readStringUntil(';');
            ble_disp_data.pressure.Systolic = (uint8_t)_sys.toInt();
            ble_disp_data.pressure.Diastolic = (uint8_t)_dia.toInt();
            ble_disp_data.pressure.Pulse = (uint8_t)_pul.toInt();
            strcpy(ble_disp_data.pressure.time, _time.c_str());

            String temp_str = "pressure:" + _sys + "," + _dia + "," + _pul + "," + _time + ";";
            str_length = strlen(temp_str.c_str());
            deleteString(offline_data_path.c_str(), 0, str_length);
            wifi_mqtt_pub_pressure_data(ble_disp_data.pressure);
        }
        else if (!_type.compareTo("weight"))
        {
            // 体重 weight,impedance,time;
            String _weight = offline_file.readStringUntil(',');
            String _impedance = offline_file.readStringUntil(',');
            String _time = offline_file.readStringUntil(';');
            ble_disp_data.weight.WeightH = (uint8_t)(_weight.toInt() >> 8 & 0xff);
            ble_disp_data.weight.WeightL = (uint8_t)(_weight.toInt() >> 0 & 0xff);
            ble_disp_data.weight.ImpedanceH = (uint8_t)(_impedance.toInt() >> 16 & 0xff);
            ble_disp_data.weight.ImpedanceM = (uint8_t)(_impedance.toInt() >> 8 & 0xff);
            ble_disp_data.weight.ImpedanceL = (uint8_t)(_impedance.toInt() >> 0 & 0xff);
            strcpy(ble_disp_data.weight.time, _time.c_str());

            String temp_str = "weight:" + _weight + "," + _impedance + "," + _time + ";";
            str_length = strlen(temp_str.c_str());
            deleteString(offline_data_path.c_str(), 0, str_length);
            wifi_mqtt_pub_weight_data(ble_disp_data.weight);
        }
        else if (!_type.compareTo("temperature"))
        {
            // 温度 temperature,time;
            String _temperature = offline_file.readStringUntil(',');
            String _time = offline_file.readStringUntil(';');
            ble_disp_data.temperature.TempH = (uint8_t)(_temperature.toInt() >> 8 & 0xff);
            ble_disp_data.temperature.TempL = (uint8_t)(_temperature.toInt() >> 0 & 0xff);
            strcpy(ble_disp_data.temperature.time, _time.c_str());

            String temp_str = "temperature:" + _temperature + "," + _time + ";";
            str_length = strlen(temp_str.c_str());
            deleteString(offline_data_path.c_str(), 0, str_length);
            wifi_mqtt_pub_temperature_data(ble_disp_data.temperature);
        }
        else if (!_type.compareTo("glucose"))
        {
            // 血糖 blood_glucose,mealstate,time;
            String _glucose = offline_file.readStringUntil(',');
            String _mealState = offline_file.readStringUntil(',');
            String _time = offline_file.readStringUntil(';');
            ble_disp_data.glucose.Glucose = _glucose.toInt();
            ble_disp_data.glucose.mealState = _mealState.toInt();
            strcpy(ble_disp_data.glucose.time, _time.c_str());
            wifi_mqtt_pub_blood_glucose_data(ble_disp_data.glucose);
        }
    }
    else
    {
        temp_flag = 0;
        offline_data_send_flag = 0;
        offline_file.close();
    }
}

/**
 * @brief 处理离线数据的函数。
 *
 * 当离线数据发送标志为 1 时，调用离线数据读取函数。
 */
void offline_data_handler()
{
    if (offline_data_send_flag == 1)
    {
        offlineDataRead();
    }
}

// 删除文件中间的字符串
bool deleteString(const char *filename, int start, int end)
{
    File file = LittleFS.open(filename, "r+");
    if (!file)
    {
        log_i("Failed to open file for writing\n");
        return false;
    }

    int fileSize = file.size();

    if (fileSize == 0)
    {
        file.close();
        return false;
    }

    char *buffer = new char[fileSize + 1];
    if (!buffer)
    {
        log_i("Failed to allocate memory\n");
        return false;
    }

    // Read the entire file into the buffer
    file.readBytes(buffer, fileSize);
    buffer[fileSize] = '\0'; // Add null terminator to make it a valid C string

    // Delete the string between start and end positions
    int lenToDelete = end - start;
    file.close();
    file = LittleFS.open(filename, "w");

    if (lenToDelete == fileSize)
    {
        file.print("");
    }
    else
    {
        memmove(&buffer[start], &buffer[end], fileSize - end + 1);
        fileSize -= lenToDelete;
        char buffer2[fileSize] = {0};
        sprintf(buffer2, "%s", buffer);
        file.print((const char *)buffer2);
    }

    file.close();
    delete[] buffer;
    return true;
}

// 在线程中下载文件
void getFile(const char *file_name)
{
    const int MAX_RETRIES = 3;
    const int MIN_FILE_SIZE = 1024; // 最小文件大小阈值 (1KB)
    int retry_count = 0;
    bool download_success = false;
    String storage_path = "/audio/" + String(file_name) + ".wav";

    while (retry_count < MAX_RETRIES && !download_success)
    {
        HTTPClient https;
        WiFiClientSecure *client = new WiFiClientSecure;
        client->setCACert(wav_client_certificate);

        https.begin(wav_file_path_base + String(file_name) + ".wav");

        int httpCode = https.GET();
        if (httpCode == HTTP_CODE_OK)
        {
            int len = https.getSize();
            log_i("file name: %s, file size: %d Byte(%d kB)", file_name, len, len / 1024);

            File file = LittleFS.open(storage_path, "w");
            if (!file)
            {
                log_e("Failed to create file: %s", storage_path.c_str());
                https.end();
                delete client;
                retry_count++;
                delay(1000);
                continue;
            }

            uint8_t buff[128] = {0};
            WiFiClient *stream = https.getStreamPtr();
            unsigned long lastYieldTime = millis();
            int bytesRead = 0;

            while (https.connected() && (len > 0 || len == -1))
            {
                if (millis() - lastYieldTime > 100)
                {
                    delay(1);
                    lastYieldTime = millis();
                }

                size_t size = stream->available();
                if (size)
                {
                    int c = stream->readBytes(buff, std::min((int)size, (int)sizeof(buff)));
                    if (c)
                    {
                        file.write(buff, c);
                        len -= c;
                        bytesRead += c;
                    }
                }
            }

            file.close();

            // 验证文件大小
            File saved_file = LittleFS.open(storage_path, "r");
            if (saved_file && saved_file.size() > MIN_FILE_SIZE)
            {
                log_i("Download success: %s (%d bytes)", storage_path.c_str(), saved_file.size());
                download_success = true;
            }
            else
            {
                log_e("File size too small: %d bytes, retrying...", saved_file.size());
                LittleFS.remove(storage_path);
            }
            saved_file.close();
        }
        else
        {
            log_i("[HTTP] GET... failed, error: %s\n", https.errorToString(httpCode).c_str());
        }

        https.end();
        delete client;

        if (!download_success)
        {
            retry_count++;
            delay(2000); // 等待2秒后重试
        }
    }

    if (!download_success)
    {
        log_e("Failed to download after %d attempts: %s", MAX_RETRIES, file_name);
    }
}

void listDir(fs::FS &fs, const char *dirname, uint8_t levels)
{
    Serial.printf("Listing directory: %s\r\n", dirname);

    File root = fs.open(dirname);
    if (!root)
    {
        Serial.println("- failed to open directory");
        return;
    }
    if (!root.isDirectory())
    {
        Serial.println(" - not a directory");
        return;
    }

    File file = root.openNextFile();
    while (file)
    {
        if (file.isDirectory())
        {
            Serial.print("  DIR : ");
            Serial.println(file.name());
            if (levels)
            {
                listDir(fs, file.path(), levels - 1);
            }
        }
        else
        {
            Serial.print("  FILE: ");
            Serial.print(file.name());
            Serial.print("\tSIZE: ");
            Serial.println(file.size());
        }
        file = root.openNextFile();
    }
}

bool get_store_wav_file(void)
{
    bool read_state = true;
    for (int i = 0; i < 10; i++)
    {

        File file = LittleFS.open(audio_path + "/" + wav_file_path[i] + ".wav", "r");
        if (!file || file.isDirectory())
        {
            log_i("- failed to open file for reading\n");
            wav_exist_flag[i] = false;
            read_state = false;
            continue;
        }
        if (!file.available())
        {
            log_i("- file is empty\n");
            file.close();
            wav_exist_flag[i] = false;
            read_state = false;
            continue;
        }
        wav_exist_flag[i] = true;
        log_i("check %s.wav file, file info: %d\n", wav_file_path[i].c_str(), file.size());
    }
    return read_state;
}

void audio_init()
{
    log_i("audio_init");
    pinMode(AUDIO_CTR, OUTPUT);
    audio.setPinout(AUDIO_BCLK, AUDIO_LRCLK, AUDIO_SDATA);
    audio.setVolume(21);
    digitalWrite(AUDIO_CTR, HIGH);
}

void audioPlayTask_func(void *parameter)
{
    char *filename = (char *)parameter;

    if (audio.isRunning())
    {
        log_e("Audio %s is running", filename);
        vTaskDelete(NULL);
        return;
    }
    // 检查文件是否存在
    if (!LittleFS.exists(filename))
    {
        log_e("Audio file %s does not exist", filename);
        vTaskDelete(NULL);
        return;
    }
    // 检查文件大小
    File file = LittleFS.open(filename, "r");
    if (!file)
    {
        log_e("Failed to open file %s", filename);
        vTaskDelete(NULL);
        return;
    }
    if (file.size() == 0)
    {
        log_e("Audio file %s is empty!", filename);
        file.close();
        vTaskDelete(NULL);
        return;
    }
    file.close();

    audio.connecttoFS(LittleFS, filename);

    while (1)
    {
        audio.loop();
        if (!audio.isRunning())
        {
            break;
        }
    }

    vTaskDelete(NULL);
}

void audio_play(const char *filename)
{
    // 如果有语音正在播放,则返回
    if (audio.isRunning())
    {
        log_e("Audio is running", filename);
        return;
    }
    // 检查文件是否存在
    if (!LittleFS.exists(filename))
    {
        log_e("Audio file %s does not exist", filename);
        return;
    }
    char *temp_file_name = new char[strlen(filename) + 1]; // 分配内存
    strcpy(temp_file_name, filename);                      // 复制字符串

    // 在外部RAM分配任务堆栈
    StackType_t *task_stack = (StackType_t *)heap_caps_malloc(8192 * sizeof(StackType_t), MALLOC_CAP_SPIRAM);
    if (task_stack == NULL)
    {
        log_e("Failed to allocate audio task stack in PSRAM");
        delete[] temp_file_name;
        return;
    }

    // 使用动态任务创建，堆栈分配在外部RAM
    xTaskCreatePinnedToCore(
        audioPlayTask_func,     // 任务函数
        "audioPlayTask",        // 任务名称
        8192,                   // 堆栈大小
        (void *)temp_file_name, // 参数
        1,                      // 优先级
        &audioPlayTask,         // 任务句柄
        0                       // 在核心0上运行
    );
}

void audio_info(const char *info)
{
    log_i("audio info %s", info);
}

void audio_web_play(const char *hostName)
{
    log_i("play web wav %s", hostName);
    audio.connecttohost(hostName);
}

void audio_eof_stream(const char *info)
{
}

std::mutex audio_mutex;
std::queue<String> audio_queue;
unsigned long last_audio_trigger = 0;
const unsigned long DEBOUNCE_DELAY = 300; // 300ms 防抖延迟

// 新的智能语音播放函数
// 语音系统状态报告函数
void voice_system_status_report(void)
{
    voice_log_info("system", "=== Voice System Status Report ===");

    // 获取存储信息
    voice_storage_info_t storage_info;
    voice_error_t err = voice_get_storage_stats(&storage_info);
    if (err == VOICE_ERR_OK)
    {
        log_i("ROM Storage: %d/%d bytes (%d%%), %d files",
              storage_info.rom_used_size, storage_info.rom_total_size,
              storage_info.rom_total_size > 0 ? (storage_info.rom_used_size * 100) / storage_info.rom_total_size : 0,
              storage_info.rom_file_count);

        log_i("LittleFS Storage: %d/%d bytes (%d%%), %d files",
              storage_info.littlefs_used_size, storage_info.littlefs_total_size,
              storage_info.littlefs_total_size > 0 ? (storage_info.littlefs_used_size * 100) / storage_info.littlefs_total_size : 0,
              storage_info.littlefs_file_count);

        log_i("Cache: %d/%d bytes (%d%%)",
              storage_info.cache_used_size, storage_info.cache_total_size,
              storage_info.cache_total_size > 0 ? (storage_info.cache_used_size * 100) / storage_info.cache_total_size : 0);
    }

    // 检查各个语音文件的可用性
    const char *voice_names[] = {
        "open_app_to_config", "network_success", "select_user", "tap_smart_config",
        "blood_pressure_data", "temperature_data", "weight_data", "blood_glucose_data",
        "blood_oxygen_data", "new_message"};

    log_i("Voice Files Status:");
    for (int i = 0; i < 10; i++)
    {
        voice_file_info_t file_info;
        err = voice_get_file_info(voice_names[i], &file_info);
        if (err == VOICE_ERR_OK)
        {
            const char *source_name[] = {"RAM", "LittleFS", "ROM", "Default", "None"};
            log_i("  %s: Available (Source: %s, Size: %d, Version: %d)",
                  voice_names[i], source_name[file_info.source],
                  file_info.file_size, file_info.version);
        }
        else
        {
            log_w("  %s: Not Available (Error: %d)", voice_names[i], err);
        }
    }

    voice_log_info("system", "=== End of Status Report ===");
}

void audio_prompt_smart(const char *voice_name)
{
    if (info_get_Voice_State() != 1)
    {
        return; // 语音功能未启用
    }

    // 使用智能播放功能
    voice_error_t err = voice_play_smart(voice_name);
    if (err != VOICE_ERR_OK)
    {
        voice_log_error(err, "audio_prompt_smart", voice_name);

        // 如果智能播放失败，回退到传统方式
        String file_name = audio_path + "/" + String(voice_name) + ".wav";
        log_w("Smart play failed for %s, falling back to traditional method", voice_name);

        std::lock_guard<std::mutex> lock(audio_mutex);
        unsigned long current_time = millis();
        if (current_time - last_audio_trigger > DEBOUNCE_DELAY)
        {
            last_audio_trigger = current_time;
            if (!audio_queue.empty() && audio_queue.front() == file_name)
            {
                return; // 避免重复添加
            }
            audio_queue.push(file_name);
        }
    }
    else
    {
        log_i("Smart voice played successfully: %s", voice_name);
    }
}

// 保持原有接口兼容性
void audio_prompt(const char *filename)
{
    // 从完整路径中提取文件名（去掉路径和扩展名）
    String voice_name = String(filename);

    // 如果传入的是完整路径，提取文件名
    int lastSlash = voice_name.lastIndexOf('/');
    if (lastSlash >= 0)
    {
        voice_name = voice_name.substring(lastSlash + 1);
    }

    // 去掉.wav扩展名
    int dotIndex = voice_name.lastIndexOf(".wav");
    if (dotIndex >= 0)
    {
        voice_name = voice_name.substring(0, dotIndex);
    }

    // 调用智能播放函数
    audio_prompt_smart(voice_name.c_str());
}

void audio_handler(void)
{
    static uint8_t pre_page_flag = 0;
    static uint8_t pre_pop_flag = 0;

    // 处于连接状态时,不执行下面后续的代码
    if (MqttState.load() != MQTT_CLIENT_CONNECTED && ServerState.load() == SERVER_CONNECTED)
    {
        return;
    }

    // 处理音频队列
    if (!audio_queue.empty() && !audio.isRunning())
    {
        std::lock_guard<std::mutex> lock(audio_mutex);
        String file_to_play = audio_queue.front();
        audio_queue.pop();
        log_i("start play wav:%s", file_to_play.c_str());
        audio_play(file_to_play.c_str());
    }

    if (audio.isRunning())
        return;

    if (msg_flag == 1)
    {
        msg_flag = 0;
        if (wav_exist_flag[NEW_MESSAGE])
        {
            audio_prompt(wav_file_path[NEW_MESSAGE].c_str());
        }
    }

    if (pop_audio_flag != 0)
    {
        if (wav_exist_flag[pop_audio_flag])
        {
            audio_prompt(wav_file_path[pop_audio_flag].c_str());
        }
        pop_audio_flag = 0;
    }

    if (get_cur_page()->body_obj == Home)
    {
        if (pre_page_flag != 1)
        {
            log_i("wav: Home page");
            pre_page_flag = 1;
            if (wav_exist_flag[OPEN_APP_TO_CONFIG])
            {
                audio_prompt(wav_file_path[OPEN_APP_TO_CONFIG].c_str());
            }
        }
    }
    else if (get_cur_page()->body_obj == Family)
    {
        if (pre_page_flag != 2)
        {
            log_i("wav: Family page");
            pre_page_flag = 2;
            if (wav_exist_flag[SELECT_USER])
            {
                audio_prompt(wav_file_path[SELECT_USER].c_str());
            }
        }
    }
    else if (get_cur_page()->body_obj == WifiConnect || get_cur_page()->body_obj == WifiConnectLv1)
    {
        if (pre_page_flag != 3)
        {
            log_i("wav: DataDisplay page");
            pre_page_flag = 3;
            if (wav_exist_flag[TAP_SMART_CONFIG] && WiFi.status() != WL_CONNECTED)
            {
                audio_prompt(wav_file_path[TAP_SMART_CONFIG].c_str());
            }
        }
    }
    else
    {
        pre_page_flag = 0;
    }

    if (get_cur_pop() == NULL)
    {
        pre_pop_flag = 0;
        return;
    }
    if (!String(get_cur_pop()->name).compareTo("WifiComplete"))
    {
        if (pre_pop_flag != 1)
        {
            log_i("wav: WifiComplete pop");
            pre_pop_flag = 1;
            if (wav_exist_flag[NETWORK_SUCCESS])
            {
                audio_prompt(wav_file_path[NETWORK_SUCCESS].c_str());
            }
        }
    }
    else if (!String(get_cur_pop()->name).compareTo("DataDisplay"))
    {
        if (pre_pop_flag != 2)
        {
            log_i("wav: DataDisplay pop");
            pre_pop_flag = 2;
            if (wav_exist_flag[2])
            {
                audio_prompt(wav_file_path[2].c_str());
            }
        }
    }
    else
    {
        pre_pop_flag = 0;
    }
}

void bleStopScan1()
{
    if (pBLEScan != nullptr)
    {
        pBLEScan->stop();
        log_i("BLE Scan stopped");
    }
}

void bleClearResults()
{
    if (pBLEScan != nullptr)
    {
        pBLEScan->clearResults();
        log_i("BLE Scan results cleared");
    }
}

void bleShutdown()
{
    bleStopScan1();
    bleClearResults();
    ble_stop_server();

    BLEDevice::deinit(true);
    log_i("BLE Device deinitialized");
}

void freeBluetoothMemory()
{
    esp_bt_mem_release(ESP_BT_MODE_BTDM);
    log_i("Bluetooth memory released");
}

void completelyDisableBluetooth()
{
    bleShutdown();
    // freeBluetoothMemory();
    log_i("Bluetooth components shut down");
}

void ble_handler()
{
    // 用于跟踪上一次OTA状态的静态变量
    static uint8_t pre_ota_flag = 0x00;

    // 处于连接状态时,不执行下面后续的代码
    if (MqttState.load() != MQTT_CLIENT_CONNECTED && ServerState.load() == SERVER_CONNECTED)
    {
        bleStopScan();
        return;
    }

    // 如果不在OTA模式
    if (otaFlag == 0)
    {
        // 如果上一次是OTA模式，现在不是，则重新初始化蓝牙
        if (pre_ota_flag == 1)
        {
            ble_init();
            pre_ota_flag = 0;
            return;
        }

        // 当WiFi处于连接或未连接状态时，允许蓝牙功能
        if (get_cur_page()->page_level >= 3 && (WifiState.load() == WIFI_DISCONNECTED || WifiState.load() == WIFI_CONNECTED))
        {
            // 执行蓝牙扫描
            ble_scan_handler();
        }
        else
        {
            // 在其他WiFi状态下，停止蓝牙扫描
            bleStopScan();
        }

        // 重置OTA标志
        pre_ota_flag = 0;
    }
    else if (otaFlag == 1) // 如果在OTA模式
    {
        // 如果刚进入OTA模式
        if (pre_ota_flag == 0)
        {
            // 完全禁用蓝牙以释放资源
            completelyDisableBluetooth();
            pre_ota_flag = 1;
        }
    }
}

void memoryMonitorTask(void *parameter)
{
    size_t pre_free_psram = 0;
    size_t pre_free_heap = 0;
    while (1)
    {
        if (psramFound())
        {
            size_t free_psram = heap_caps_get_free_size(MALLOC_CAP_SPIRAM);
            size_t free_heap = heap_caps_get_free_size(MALLOC_CAP_INTERNAL);
            if (pre_free_psram != free_psram || pre_free_heap != free_heap)
            {
                log_i("free PSRAM: %d Bytes, free heap: %d Bytes\n", free_psram, free_heap);
                pre_free_psram = free_psram;
                pre_free_heap = free_heap;
            }
        }
        vTaskDelay(pdMS_TO_TICKS(5000)); // 每5秒检查一次
    }
}

static void psram_info()
{
    // 检查PSRAM是否可用
    if (psramFound())
    {
        xTaskCreate(
            memoryMonitorTask, // 任务函数
            "MemoryMonitor",   // 任务名称
            4096,              // 堆栈大小
            NULL,              // 任务参数
            1,                 // 任务优先级
            NULL               // 任务句柄
        );
    }
}

// 添加重启原因追踪
void checkResetReason()
{
    esp_reset_reason_t reason = esp_reset_reason();
    switch (reason)
    {
    case ESP_RST_UNKNOWN:
        log_w("Reset reason: UNKNOWN");
        break;
    case ESP_RST_POWERON:
        log_w("Reset reason: POWER ON");
        break;
    case ESP_RST_EXT:
        log_w("Reset reason: EXTERNAL");
        break;
    case ESP_RST_SW:
        log_w("Reset reason: SOFTWARE");
        break;
    case ESP_RST_PANIC:
        log_w("Reset reason: PANIC");
        break;
    case ESP_RST_INT_WDT:
        log_w("Reset reason: INT WDT");
        break;
    case ESP_RST_TASK_WDT:
        log_w("Reset reason: TASK WDT");
        break;
    case ESP_RST_WDT:
        log_w("Reset reason: REBT WDT");
        break;
    case ESP_RST_DEEPSLEEP:
        log_w("Reset reason: DEEP SLEEP");
        break;
    case ESP_RST_BROWNOUT:
        log_w("Reset reason: BROWNOUT");
        break;
    case ESP_RST_SDIO:
        log_w("Reset reason: SDIO");
        break;
    }
}
bool exit_btn_state_after_repower = false;

static void btn_state_check(void)
{
    if (digitalRead(KEY_EXIT) == HIGH)
    {
        exit_btn_state_after_repower = true;
    }
    else
    {
        exit_btn_state_after_repower = false;
    }
}

void setup()
{
    power_init();
    power_on(true);
    lcd_bg_control(LIGHT_OFF);
    heap_caps_malloc_extmem_enable(1);
    esp_coex_preference_set(ESP_COEX_PREFER_BT);
    delay(1000);
    btn_state_check();

    system_init(); // 系统初始化

    wifi_init();      // wifi初始化
    ble_init();       // ble初始化
    lvgl_init();      // lvgl初始化
    wifi_data_read(); // wifi数据读取
    audio_init();
    versionUpdataToLabel();

    restore_data();

    psram_info();

    lcd_bg_control(LIGHT_ON); // 背光控制初始化
}

void loop()
{
    if (pre_system_state != system_state)
    {
        if (system_state == true)
        {
            log_i("exit sleep mode");
            lcd_bg_control(LIGHT_ON);
            lv_set_indev_state(1);
        }
        else
        {
            log_i("enter sleep mode");
            lcd_bg_control(LIGHT_OFF);
            lv_set_indev_state(0);
        }
        pre_system_state = system_state;
    }
    static bool wav_download_triggered = false;
    if (WifiState.load() == WIFI_CONNECTED)
    {
        if (!wav_download_triggered)
        {
            re_download_wav_file();
            wav_download_triggered = true;
        }
    }
    else
    {
        wav_download_triggered = false;
    }

    if (exit_btn_state_after_repower == true)
    {
        btn_state_check();
    }

    CM_EXECUTE_INTERVAL(5, {
        if (system_state == true)
        {
            if (exit_btn_state_after_repower == false)
            {
                KeyExit.tick();
                KeyChange.tick();
            }
            lv_timer_handler();
        }

        button_change_check();
    });

    CM_EXECUTE_INTERVAL(1000, {
        // wifi重连功能
        wifi_reconnect_handler();

        wifi_running_handler();

        // 处理音频相关功能
        audio_handler();

        // 处理蓝牙相关功能
        ble_handler();

        // 设备绑定回复句柄
        device_binding_reply();

        // ota升级句柄
        ota_handler();

        // 离线数据处理句柄
        offline_data_handler();

        // 状态栏更新句柄
        status_bar_disp_handler();

        // 数据弹窗处理句柄
        data_popup_handler();

        // 自动熄屏处理
        sleep_screen_handler();
    });

    // 语音系统维护任务（每30秒执行一次）
    CM_EXECUTE_INTERVAL(30000, {
        // 监控存储空间
        voice_monitor_storage();

        // 检查并更新语音文件（仅在WiFi连接时）
        if (WifiState.load() == WIFI_CONNECTED)
        {
            static uint32_t last_voice_update_check = 0;
            uint32_t current_time = millis();

            // 每10分钟检查一次更新
            if (current_time - last_voice_update_check >= 600000)
            {
                last_voice_update_check = current_time;

                // 异步检查更新，避免阻塞主循环
                static bool update_in_progress = false;
                if (!update_in_progress)
                {
                    update_in_progress = true;

                    // 这里可以创建一个任务来处理更新
                    xTaskCreatePinnedToCore([](void *param)
                                            {
                        voice_error_t err = voice_update_all_from_cloud();
                        if (err == VOICE_ERR_OK) {
                            voice_log_info("maintenance", "Voice files updated successfully");
                        } else {
                            voice_log_warning("maintenance", "Voice files update failed");
                        }

                        // 重置更新标志
                        bool* update_flag = (bool*)param;
                        *update_flag = false;

                        vTaskDelete(NULL); }, "VoiceUpdate", 4096, &update_in_progress, 1, NULL, 0);
                }
            }
        }
    });
}