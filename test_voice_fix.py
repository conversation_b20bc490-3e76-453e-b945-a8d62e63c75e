#!/usr/bin/env python3
"""
Home页面语音播报修复测试脚本
用于验证语音播报功能是否正常工作
"""

import serial
import time
import re
import sys
from typing import List, Dict

class VoiceTestMonitor:
    def __init__(self, port: str = "COM3", baudrate: int = 115200):
        """
        初始化串口监控器
        
        Args:
            port: 串口端口号
            baudrate: 波特率
        """
        self.port = port
        self.baudrate = baudrate
        self.ser = None
        self.test_results = {}
        
    def connect(self) -> bool:
        """连接串口"""
        try:
            self.ser = serial.Serial(self.port, self.baudrate, timeout=1)
            print(f"已连接到串口 {self.port}")
            return True
        except Exception as e:
            print(f"连接串口失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        if self.ser and self.ser.is_open:
            self.ser.close()
            print("串口已断开")
    
    def read_line(self) -> str:
        """读取一行数据"""
        if self.ser and self.ser.is_open:
            try:
                line = self.ser.readline().decode('utf-8', errors='ignore').strip()
                return line
            except Exception as e:
                print(f"读取数据失败: {e}")
                return ""
        return ""
    
    def monitor_voice_events(self, duration: int = 60) -> Dict[str, List[str]]:
        """
        监控语音事件
        
        Args:
            duration: 监控时长（秒）
            
        Returns:
            检测到的语音事件字典
        """
        print(f"开始监控语音事件，持续 {duration} 秒...")
        
        voice_events = {
            'home_page': [],
            'family_page': [],
            'wifi_config': [],
            'wifi_complete': [],
            'new_message': [],
            'smart_voice': [],
            'traditional_voice': [],
            'errors': []
        }
        
        start_time = time.time()
        
        while time.time() - start_time < duration:
            line = self.read_line()
            if not line:
                continue
                
            # 检测Home页面语音事件
            if "wav: Home page" in line:
                voice_events['home_page'].append(line)
                print(f"[HOME] {line}")
                
            # 检测Family页面语音事件
            elif "wav: Family page" in line:
                voice_events['family_page'].append(line)
                print(f"[FAMILY] {line}")
                
            # 检测WiFi配置页面语音事件
            elif "wav: WiFi Config page" in line:
                voice_events['wifi_config'].append(line)
                print(f"[WIFI] {line}")
                
            # 检测WiFi完成弹窗语音事件
            elif "wav: WifiComplete pop" in line:
                voice_events['wifi_complete'].append(line)
                print(f"[WIFI_COMPLETE] {line}")
                
            # 检测智能语音播放
            elif "trying smart voice play" in line:
                voice_events['smart_voice'].append(line)
                print(f"[SMART] {line}")
                
            # 检测传统语音播放
            elif "start play wav:" in line:
                voice_events['traditional_voice'].append(line)
                print(f"[TRADITIONAL] {line}")
                
            # 检测语音播放成功
            elif "Voice played successfully:" in line:
                voice_events['smart_voice'].append(line)
                print(f"[SUCCESS] {line}")
                
            # 检测错误
            elif "Failed to play voice" in line or "Audio file" in line and "does not exist" in line:
                voice_events['errors'].append(line)
                print(f"[ERROR] {line}")
        
        return voice_events
    
    def analyze_results(self, events: Dict[str, List[str]]) -> Dict[str, str]:
        """分析测试结果"""
        results = {}
        
        # 分析Home页面
        if events['home_page']:
            if any("start play wav:" in event for event in events['traditional_voice']) or \
               any("Voice played successfully:" in event for event in events['smart_voice']):
                results['home_page'] = "PASS - 语音播放成功"
            else:
                results['home_page'] = "FAIL - 语音播放失败"
        else:
            results['home_page'] = "NOT_TESTED - 未检测到Home页面事件"
        
        # 分析Family页面
        if events['family_page']:
            if any("start play wav:" in event for event in events['traditional_voice']) or \
               any("Voice played successfully:" in event for event in events['smart_voice']):
                results['family_page'] = "PASS - 语音播放成功"
            else:
                results['family_page'] = "FAIL - 语音播放失败"
        else:
            results['family_page'] = "NOT_TESTED - 未检测到Family页面事件"
        
        # 分析智能语音回退
        smart_attempts = len(events['smart_voice'])
        if smart_attempts > 0:
            results['smart_fallback'] = f"DETECTED - 智能语音回退尝试 {smart_attempts} 次"
        else:
            results['smart_fallback'] = "NOT_DETECTED - 未检测到智能语音回退"
        
        # 分析错误
        if events['errors']:
            results['errors'] = f"DETECTED - 发现 {len(events['errors'])} 个错误"
        else:
            results['errors'] = "NONE - 无错误"
        
        return results
    
    def print_report(self, events: Dict[str, List[str]], results: Dict[str, str]):
        """打印测试报告"""
        print("\n" + "="*60)
        print("语音播报修复测试报告")
        print("="*60)
        
        print("\n测试结果:")
        for key, result in results.items():
            status = result.split(" - ")[0]
            description = result.split(" - ")[1] if " - " in result else ""
            
            if status == "PASS":
                print(f"  ✅ {key}: {description}")
            elif status == "FAIL":
                print(f"  ❌ {key}: {description}")
            elif status == "DETECTED":
                print(f"  🔍 {key}: {description}")
            elif status == "NOT_DETECTED":
                print(f"  ⚠️  {key}: {description}")
            else:
                print(f"  ℹ️  {key}: {description}")
        
        print(f"\n事件统计:")
        for event_type, event_list in events.items():
            if event_list:
                print(f"  {event_type}: {len(event_list)} 个事件")
        
        if events['errors']:
            print(f"\n错误详情:")
            for error in events['errors']:
                print(f"  - {error}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        port = sys.argv[1]
    else:
        port = input("请输入串口端口号 (默认 COM3): ").strip() or "COM3"
    
    monitor = VoiceTestMonitor(port)
    
    if not monitor.connect():
        return
    
    try:
        print("\n请在设备上进行以下操作来测试语音播报:")
        print("1. 进入Home页面")
        print("2. 进入Family页面")
        print("3. 进入WiFi配置页面")
        print("4. 触发WiFi连接成功")
        print("5. 触发新消息提示")
        print("\n开始监控...")
        
        events = monitor.monitor_voice_events(60)
        results = monitor.analyze_results(events)
        monitor.print_report(events, results)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    finally:
        monitor.disconnect()

if __name__ == "__main__":
    main()
